# Notification Service Detailed Design

## Event Schema Definitions

### Voucher Events

```protobuf
// voucher-events topic
message VoucherCreatedEvent {
  string event_id = 1;
  string event_type = 2; // "voucher.created"
  google.protobuf.Timestamp timestamp = 3;
  uint64 voucher_id = 4;
  string voucher_code = 5;
  string title = 6;
  uint64 created_by = 7;
  google.protobuf.Timestamp valid_from = 8;
  google.protobuf.Timestamp valid_until = 9;
  double discount_value = 10;
  string user_eligibility_type = 11;
}

message VoucherUsedEvent {
  string event_id = 1;
  string event_type = 2; // "voucher.used"
  google.protobuf.Timestamp timestamp = 3;
  uint64 voucher_id = 4;
  string voucher_code = 5;
  uint64 user_id = 6;
  uint64 order_id = 7;
  double discount_amount = 8;
  double order_amount = 9;
}

message VoucherExpiringEvent {
  string event_id = 1;
  string event_type = 2; // "voucher.expiring"
  google.protobuf.Timestamp timestamp = 3;
  uint64 voucher_id = 4;
  string voucher_code = 5;
  google.protobuf.Timestamp expires_at = 6;
  int32 hours_until_expiry = 7;
  repeated uint64 eligible_user_ids = 8;
}

message VoucherStatusChangedEvent {
  string event_id = 1;
  string event_type = 2; // "voucher.status_changed"
  google.protobuf.Timestamp timestamp = 3;
  uint64 voucher_id = 4;
  string voucher_code = 5;
  string old_status = 6;
  string new_status = 7;
  string reason = 8;
}
```

### Order Events

```protobuf
// order-events topic
message OrderCreatedEvent {
  string event_id = 1;
  string event_type = 2; // "order.created"
  google.protobuf.Timestamp timestamp = 3;
  uint64 order_id = 4;
  uint64 user_id = 5;
  double order_amount = 6;
  repeated OrderItem items = 7;
  string status = 8;
}

message VoucherAppliedEvent {
  string event_id = 1;
  string event_type = 2; // "order.voucher_applied"
  google.protobuf.Timestamp timestamp = 3;
  uint64 order_id = 4;
  uint64 user_id = 5;
  uint64 voucher_id = 6;
  string voucher_code = 7;
  double original_amount = 8;
  double discount_amount = 9;
  double final_amount = 10;
}

message VoucherApplicationFailedEvent {
  string event_id = 1;
  string event_type = 2; // "order.voucher_failed"
  google.protobuf.Timestamp timestamp = 3;
  uint64 order_id = 4;
  uint64 user_id = 5;
  string voucher_code = 6;
  string failure_reason = 7;
  double order_amount = 8;
}
```

### User Events

```protobuf
// user-events topic
message UserCreatedEvent {
  string event_id = 1;
  string event_type = 2; // "user.created"
  google.protobuf.Timestamp timestamp = 3;
  uint64 user_id = 4;
  string email = 5;
  string name = 6;
  string user_type = 7;
}

message UserTypeChangedEvent {
  string event_id = 1;
  string event_type = 2; // "user.type_changed"
  google.protobuf.Timestamp timestamp = 3;
  uint64 user_id = 4;
  string email = 5;
  string old_type = 6;
  string new_type = 7;
  string reason = 8;
}
```

## Database Models

### Notification Model
```go
type NotificationStatus string
type NotificationChannel string
type NotificationType string

const (
    NotificationStatusPending   NotificationStatus = "PENDING"
    NotificationStatusSent      NotificationStatus = "SENT"
    NotificationStatusFailed    NotificationStatus = "FAILED"
    NotificationStatusRead      NotificationStatus = "READ"
    NotificationStatusCancelled NotificationStatus = "CANCELLED"
)

const (
    ChannelEmail NotificationChannel = "EMAIL"
    ChannelPush  NotificationChannel = "PUSH"
    ChannelSMS   NotificationChannel = "SMS"
    ChannelInApp NotificationChannel = "IN_APP"
)

const (
    TypeVoucherCreated        NotificationType = "VOUCHER_CREATED"
    TypeVoucherExpiring       NotificationType = "VOUCHER_EXPIRING"
    TypeVoucherUsed          NotificationType = "VOUCHER_USED"
    TypeOrderConfirmation    NotificationType = "ORDER_CONFIRMATION"
    TypeVoucherApplied       NotificationType = "VOUCHER_APPLIED"
    TypeVoucherFailed        NotificationType = "VOUCHER_FAILED"
    TypeUserWelcome          NotificationType = "USER_WELCOME"
    TypeUserTypeUpgrade      NotificationType = "USER_TYPE_UPGRADE"
)

type Notification struct {
    ID           uint64              `gorm:"primaryKey;autoIncrement"`
    UserID       uint64              `gorm:"not null;index"`
    Type         NotificationType    `gorm:"type:varchar(50);not null"`
    Title        string              `gorm:"type:varchar(255);not null"`
    Message      string              `gorm:"type:text;not null"`
    Status       NotificationStatus  `gorm:"type:varchar(20);not null;default:'PENDING'"`
    Channel      NotificationChannel `gorm:"type:varchar(20);not null"`
    Metadata     datatypes.JSON      `gorm:"type:jsonb"`
    ScheduledAt  *time.Time          `gorm:"index"`
    SentAt       *time.Time
    ReadAt       *time.Time
    CreatedAt    time.Time           `gorm:"not null"`
    UpdatedAt    time.Time           `gorm:"not null"`
}
```

### Template Model
```go
type NotificationTemplate struct {
    ID               uint64           `gorm:"primaryKey;autoIncrement"`
    TemplateKey      string           `gorm:"type:varchar(100);not null;unique"`
    Type             NotificationType `gorm:"type:varchar(50);not null"`
    TitleTemplate    string           `gorm:"type:text;not null"`
    MessageTemplate  string           `gorm:"type:text;not null"`
    DefaultChannel   NotificationChannel `gorm:"type:varchar(20);not null"`
    TemplateVariables datatypes.JSON  `gorm:"type:jsonb"`
    IsActive         bool             `gorm:"not null;default:true"`
    CreatedAt        time.Time        `gorm:"not null"`
    UpdatedAt        time.Time        `gorm:"not null"`
}
```

### User Preferences Model
```go
type UserNotificationPreference struct {
    ID              uint64              `gorm:"primaryKey;autoIncrement"`
    UserID          uint64              `gorm:"not null;index"`
    Type            NotificationType    `gorm:"type:varchar(50);not null"`
    PreferredChannel NotificationChannel `gorm:"type:varchar(20);not null"`
    IsEnabled       bool                `gorm:"not null;default:true"`
    ChannelSettings datatypes.JSON      `gorm:"type:jsonb"`
    CreatedAt       time.Time           `gorm:"not null"`
    UpdatedAt       time.Time           `gorm:"not null"`
}
```

### Delivery Log Model
```go
type DeliveryStatus string

const (
    DeliveryStatusPending   DeliveryStatus = "PENDING"
    DeliveryStatusDelivered DeliveryStatus = "DELIVERED"
    DeliveryStatusFailed    DeliveryStatus = "FAILED"
    DeliveryStatusRetrying  DeliveryStatus = "RETRYING"
)

type NotificationDeliveryLog struct {
    ID               uint64         `gorm:"primaryKey;autoIncrement"`
    NotificationID   uint64         `gorm:"not null;index"`
    Channel          NotificationChannel `gorm:"type:varchar(20);not null"`
    Status           DeliveryStatus `gorm:"type:varchar(20);not null"`
    ProviderResponse string         `gorm:"type:text"`
    RetryCount       int            `gorm:"not null;default:0"`
    AttemptedAt      time.Time      `gorm:"not null"`
    DeliveredAt      *time.Time
    
    Notification *Notification `gorm:"foreignKey:NotificationID"`
}
```

## Service Architecture Components

### 1. Event Consumers
- **VoucherEventConsumer**: Processes voucher-related events
- **OrderEventConsumer**: Processes order-related events  
- **UserEventConsumer**: Processes user-related events

### 2. Notification Processors
- **TemplateProcessor**: Renders notification content from templates
- **PreferenceProcessor**: Applies user notification preferences
- **SchedulingProcessor**: Handles delayed/scheduled notifications

### 3. Delivery Channels
- **EmailChannel**: Email notification delivery
- **PushChannel**: Push notification delivery
- **SMSChannel**: SMS notification delivery
- **InAppChannel**: In-app notification storage

### 4. Supporting Services
- **TemplateService**: Manages notification templates
- **PreferenceService**: Manages user preferences
- **DeliveryService**: Coordinates notification delivery
- **AnalyticsService**: Tracks notification metrics

## Redis Caching Strategy

### Cache Keys Structure
```
notification:template:{template_key} -> NotificationTemplate
notification:preference:{user_id}:{type} -> UserNotificationPreference
notification:ratelimit:{user_id}:{type} -> RateLimitCounter
notification:delivery:{notification_id} -> DeliveryStatus
```

### Cache TTL Configuration
- Templates: 1 hour (rarely change)
- User Preferences: 30 minutes
- Rate Limits: 1 hour sliding window
- Delivery Status: 24 hours

## Configuration Structure

```yaml
service:
  name: "notification-service"
  version: "1.0.0"
  environment: "development"
  port: 8080
  grpc_port: 50051

kafka:
  brokers: ["kafka:9092"]
  group_id: "notification-service"
  topics:
    voucher_events: "voucher-events"
    order_events: "order-events"
    user_events: "user-events"
    notification_events: "notification-events"

channels:
  email:
    provider: "smtp"
    smtp_host: "smtp.gmail.com"
    smtp_port: 587
    from_address: "<EMAIL>"
  push:
    provider: "fcm"
    server_key: "${FCM_SERVER_KEY}"
  sms:
    provider: "twilio"
    account_sid: "${TWILIO_ACCOUNT_SID}"
    auth_token: "${TWILIO_AUTH_TOKEN}"

rate_limiting:
  default_limit: 10
  window_minutes: 60
  per_type_limits:
    VOUCHER_EXPIRING: 5
    ORDER_CONFIRMATION: 20
```
