package dto

import (
	"time"

	proto_order_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/order/v1"
)

type OrderItemRequest struct {
	ProductID uint64  `json:"product_id" validate:"required"`
	Quantity  int32   `json:"quantity" validate:"required,min=1"`
	Price     float64 `json:"price" validate:"required,gt=0"`
}

type CreateOrderRequest struct {
	UserID         uint64             `json:"user_id" validate:"required"`
	Items          []OrderItemRequest `json:"items" validate:"required,min=1"`
	OrderAmount    float64            `json:"order_amount" validate:"required,gt=0"`
	OrderTimestamp time.Time          `json:"order_timestamp" validate:"required"`
	VoucherCode    *string            `json:"voucher_code,omitempty"`
}

type OrderItemResponse struct {
	ProductID uint64  `json:"product_id"`
	Quantity  int32   `json:"quantity"`
	Price     float64 `json:"price"`
}

type OrderResponse struct {
	ID                 uint64              `json:"id"`
	UserID             uint64              `json:"user_id"`
	Items              []OrderItemResponse `json:"items"`
	OrderAmount        float64             `json:"order_amount"`
	CalculationStatus  string              `json:"calculation_status"`
	CalculationMessage string              `json:"calculation_message"`
	CreatedAt          time.Time           `json:"created_at"`
	UpdatedAt          time.Time           `json:"updated_at"`
	AppliedVoucherID   *uint64             `json:"applied_voucher_id,omitempty"`
	AppliedVoucherCode *string             `json:"applied_voucher_code,omitempty"`
	DiscountAmount     *float64            `json:"discount_amount,omitempty"`
}

type ListOrdersRequest struct {
	UserID *uint64 `json:"user_id,omitempty"`
	Page   int32   `json:"page" validate:"min=1"`
	Limit  int32   `json:"limit" validate:"min=1,max=100"`
	Search *string `json:"search,omitempty"`
}

type ListOrdersByVoucherRequest struct {
	VoucherID uint64 `json:"voucher_id" validate:"required"`
	Page      int32  `json:"page" validate:"min=1"`
	Limit     int32  `json:"limit" validate:"min=1,max=100"`
}

type UpdateOrderStatusRequest struct {
	OrderID uint64 `json:"order_id" validate:"required"`
	Status  string `json:"status" validate:"required"`
}

type PaginationResponse struct {
	CurrentPage int32 `json:"current_page"`
	PageSize    int32 `json:"page_size"`
	TotalItems  int64 `json:"total_items"`
	TotalPages  int32 `json:"total_pages"`
	HasNext     bool  `json:"has_next"`
	HasPrevious bool  `json:"has_previous"`
}

type ListOrdersResponse struct {
	Orders     []OrderResponse    `json:"orders"`
	Pagination PaginationResponse `json:"pagination"`
}

type UserOrderCountResponse struct {
	OrderCount int64 `json:"order_count"`
}

type UserVoucherUsageCountResponse struct {
	UsageCount int32 `json:"usage_count"`
}

// Conversion functions
func ToOrderResponse(order *proto_order_v1.Order) *OrderResponse {
	if order == nil {
		return nil
	}

	var items []OrderItemResponse
	for _, item := range order.Items {
		items = append(items, OrderItemResponse{
			ProductID: item.ProductId,
			Quantity:  item.Quantity,
			Price:     item.Price,
		})
	}

	response := &OrderResponse{
		ID:                 order.Id,
		UserID:             order.UserId,
		Items:              items,
		OrderAmount:        order.OrderAmount,
		CalculationStatus:  order.CalculationStatus,
		CalculationMessage: order.CalculationMessage,
		CreatedAt:          order.CreatedAt.AsTime(),
		UpdatedAt:          order.UpdatedAt.AsTime(),
	}

	if order.AppliedVoucherId != nil {
		response.AppliedVoucherID = order.AppliedVoucherId
	}

	if order.AppliedVoucherCode != nil {
		response.AppliedVoucherCode = order.AppliedVoucherCode
	}

	if order.DiscountAmount != nil {
		response.DiscountAmount = order.DiscountAmount
	}

	return response
}

func ToOrderItemProto(item OrderItemRequest) *proto_order_v1.OrderItem {
	return &proto_order_v1.OrderItem{
		ProductId: item.ProductID,
		Quantity:  item.Quantity,
		Price:     item.Price,
	}
}

func ToListOrdersPaginationResponse(response *proto_order_v1.ListOrdersResponse) PaginationResponse {
	if response.Pagination == nil {
		return PaginationResponse{}
	}
	return PaginationResponse{
		CurrentPage: response.Pagination.CurrentPage,
		PageSize:    response.Pagination.PageSize,
		TotalItems:  response.Pagination.TotalItems,
		TotalPages:  response.Pagination.TotalPages,
		HasNext:     response.Pagination.HasNext,
		HasPrevious: response.Pagination.HasPrevious,
	}
}

func ToListOrdersByVoucherPaginationResponse(response *proto_order_v1.ListOrdersByVoucherResponse) PaginationResponse {
	if response.Pagination == nil {
		return PaginationResponse{}
	}
	return PaginationResponse{
		CurrentPage: response.Pagination.CurrentPage,
		PageSize:    response.Pagination.PageSize,
		TotalItems:  response.Pagination.TotalItems,
		TotalPages:  response.Pagination.TotalPages,
		HasNext:     response.Pagination.HasNext,
		HasPrevious: response.Pagination.HasPrevious,
	}
}
