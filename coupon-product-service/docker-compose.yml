services:
  postgres-product:
    image: postgres:16-alpine
    container_name: postgres-product
    restart: unless-stopped
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-coupon}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-coupon}
      POSTGRES_DB: ${POSTGRES_DB:-product_db}
    ports:
      - "5435:5432"
    volumes:
      - postgres-product-data:/var/lib/postgresql/data
    healthcheck:
      test:
        [
          "CMD-SHELL",
          "pg_isready -U ${POSTGRES_USER:-coupon} -d ${POSTGRES_DB:-product_db}",
        ]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - coupon-network

  redis-product:
    image: redis:7-alpine
    container_name: redis-product
    restart: unless-stopped
    command: ["redis-server", "--requirepass", "${REDIS_PASSWORD:-}"]
    ports:
      - "6382:6379"
    volumes:
      - redis-product-data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    networks:
      - coupon-network

  # jaeger:
  #   image: jaegertracing/all-in-one:1.56
  #   container_name: jaeger
  #   restart: unless-stopped
  #   ports:
  #     - "16686:16686"
  #     - "6831:6831/udp"
  #   networks:
  #     - coupon-network

  product-service:
    build:
      context: .
      args:
        GITLAB_USER: ${GITLAB_USER}
        GITLAB_TOKEN: ${GITLAB_TOKEN}
    container_name: product-service
    image: registry-gitlab.zalopay.vn/phunn4/coupon-product-service
    depends_on:
      postgres-product:
        condition: service_healthy
      redis-product:
        condition: service_healthy
      # jaeger:
      #   condition: service_started
    env_file:
      - .env
    ports:
      - "8084:8080"
      - "50055:50051"
      - "2116:2112"
    restart: unless-stopped
    networks:
      - coupon-network

volumes:
  postgres-product-data:
  redis-product-data:

networks:
  coupon-network:
    name: coupon-network
    driver: bridge
