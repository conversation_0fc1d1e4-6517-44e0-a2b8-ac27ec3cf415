# Coupon Microservice Deployment Guide

This guide provides step-by-step instructions for deploying the complete coupon microservice system with event-driven notifications.

## Architecture Overview

The system consists of:

### Core Services
- **auth-service**: Service registry and authentication
- **user-service**: User management with JWT authentication
- **product-service**: Product and category management
- **voucher-service**: Voucher eligibility and usage tracking
- **order-service**: Order processing with voucher application
- **notification-service**: Event-driven notification processing
- **api-gateway**: HTTP REST API gateway

### Infrastructure
- **Kafka + Zookeeper**: Event streaming platform
- **PostgreSQL**: Database per service
- **Redis**: Caching per service
- **Jaeger**: Distributed tracing
- **Adminer**: Database management UI

## Prerequisites

- Docker and Docker Compose
- Make (for build automation)
- Git

## Quick Start

### 1. Clone and Setup

```bash
git clone <repository-url>
cd coupon-microservice
make setup
```

### 2. Start Infrastructure

```bash
# Start shared infrastructure (<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
make infrastructure-up

# Create Kafka topics
make create-topics
```

### 3. Start Services

```bash
# Start all microservices
make services-up
```

### 4. Register Notification Service

```bash
# Register notification service with auth service
make register-notification-service
```

### 5. Verify Deployment

```bash
# Check status of all services
make status
```

## Access Points

Once deployed, you can access:

- **API Gateway**: http://localhost:8080
- **Kafka UI**: http://localhost:8090
- **Jaeger Tracing**: http://localhost:16686
- **Adminer Database UI**: http://localhost:8091

### Service Endpoints

| Service | HTTP Port | gRPC Port | Health Check |
|---------|-----------|-----------|--------------|
| API Gateway | 8080 | - | http://localhost:8080/health |
| Auth Service | 8081 | 50052 | http://localhost:8081/health |
| User Service | 8082 | 50053 | http://localhost:8082/health |
| Product Service | 8083 | 50054 | http://localhost:8083/health |
| Voucher Service | 8084 | 50055 | http://localhost:8084/health |
| Order Service | 8085 | 50056 | http://localhost:8085/health |
| Notification Service | 8086 | 50057 | http://localhost:8086/health |

## Event-Driven Notification Flow

### Event Publishers

1. **Voucher Service** publishes:
   - `voucher.created` - When new voucher is created
   - `voucher.used` - When voucher is applied to order
   - `voucher.expiring` - For vouchers expiring soon
   - `voucher.status_changed` - When voucher status changes

2. **Order Service** publishes:
   - `order.created` - When order is created
   - `order.voucher_applied` - When voucher is successfully applied
   - `order.voucher_failed` - When voucher application fails

3. **User Service** publishes:
   - `user.created` - When new user registers
   - `user.type_changed` - When user type changes (NEW → VIP)
   - `user.login` - When user logs in

### Event Consumers

**Notification Service** consumes all events and:
- Creates notifications from templates
- Applies user preferences for channels
- Delivers via Email or In-App
- Retries failed deliveries
- Tracks delivery status

## Configuration

### Environment Variables

Each service requires specific environment variables. Key ones include:

```bash
# Service Credentials (generated during registration)
AUTH_SERVICE_CLIENT_ID=<generated>
AUTH_SERVICE_CLIENT_KEY=<generated>
USER_SERVICE_CLIENT_ID=<generated>
USER_SERVICE_CLIENT_KEY=<generated>
# ... etc for each service

# Database Credentials
POSTGRES_USER=coupon
POSTGRES_PASSWORD=coupon
POSTGRES_DB=<service>_db

# Redis Credentials
REDIS_PASSWORD=<service>_redis_password

# Notification Channels (optional)
SMTP_USERNAME=<email>
SMTP_PASSWORD=<password>
SMTP_FROM_ADDRESS=<email>
```

### Kafka Topics

The system uses these Kafka topics:
- `voucher-events` - Voucher-related events
- `order-events` - Order-related events  
- `user-events` - User-related events
- `notification-events` - Notification system events

## Testing the System

### 1. Create a User

```bash
curl -X POST http://localhost:8080/api/v1/users/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "name": "Test User"
  }'
```

### 2. Create a Voucher

```bash
curl -X POST http://localhost:8080/api/v1/vouchers \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <jwt-token>" \
  -d '{
    "title": "Welcome Discount",
    "voucher_code": "WELCOME10",
    "discount_value": 10.0,
    "discount_type": "PERCENTAGE"
  }'
```

### 3. Create an Order

```bash
curl -X POST http://localhost:8080/api/v1/orders \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <jwt-token>" \
  -d '{
    "items": [
      {
        "product_id": 1,
        "quantity": 2,
        "price": 25.0
      }
    ],
    "voucher_code": "WELCOME10"
  }'
```

### 4. Check Notifications

```bash
curl -X GET http://localhost:8080/api/v1/notifications \
  -H "Authorization: Bearer <jwt-token>"
```

## Monitoring and Observability

### Kafka Monitoring

- **Kafka UI**: http://localhost:8090
- Monitor topic throughput, consumer lag, and message flow

### Distributed Tracing

- **Jaeger**: http://localhost:16686
- Trace requests across all services
- Monitor performance and identify bottlenecks

### Database Management

- **Adminer**: http://localhost:8091
- Access all service databases
- View notification templates, user preferences

### Health Checks

All services expose health endpoints:
```bash
curl http://localhost:808X/health
```

## Troubleshooting

### Common Issues

1. **Services not starting**: Check Docker logs
   ```bash
   make logs-<service>
   ```

2. **Kafka connection issues**: Ensure infrastructure is up
   ```bash
   make infrastructure-up
   make create-topics
   ```

3. **Authentication failures**: Verify service registration
   ```bash
   make register-notification-service
   ```

4. **Event not flowing**: Check Kafka UI for topic activity

### Cleanup and Reset

```bash
# Stop everything and clean up
make clean-all

# Start fresh
make infrastructure-up
make create-topics
make services-up
```

## Production Considerations

1. **Security**: Use proper secrets management
2. **Scaling**: Configure Kafka partitions and consumer groups
3. **Monitoring**: Set up proper alerting for consumer lag
4. **Backup**: Regular database and Kafka topic backups
5. **SSL/TLS**: Enable encryption for all communications
6. **Resource Limits**: Set appropriate CPU/memory limits

## Next Steps

1. Implement additional notification channels
2. Add notification scheduling and batching
3. Implement notification analytics and reporting
4. Add A/B testing for notification templates
5. Implement notification preferences UI
