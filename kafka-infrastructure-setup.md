# Kafka Infrastructure Setup

## Docker Compose Configuration

### Main Infrastructure docker-compose.yml
```yaml
version: '3.8'

services:
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    container_name: zookeeper
    restart: unless-stopped
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    ports:
      - "2181:2181"
    volumes:
      - zookeeper-data:/var/lib/zookeeper/data
      - zookeeper-logs:/var/lib/zookeeper/log
    networks:
      - coupon-network
    healthcheck:
      test: ["CMD", "nc", "-z", "localhost", "2181"]
      interval: 10s
      timeout: 5s
      retries: 5

  kafka:
    image: confluentinc/cp-kafka:7.4.0
    container_name: kafka
    restart: unless-stopped
    depends_on:
      zookeeper:
        condition: service_healthy
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_LISTENERS: PLAINTEXT://0.0.0.0:29092,PLAINTEXT_HOST://0.0.0.0:9092
      KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'false'
      KAFKA_LOG_RETENTION_HOURS: 168
      KAFKA_LOG_SEGMENT_BYTES: **********
      KAFKA_LOG_RETENTION_CHECK_INTERVAL_MS: 300000
    ports:
      - "9092:9092"
      - "29092:29092"
    volumes:
      - kafka-data:/var/lib/kafka/data
    networks:
      - coupon-network
    healthcheck:
      test: ["CMD", "kafka-topics", "--bootstrap-server", "localhost:9092", "--list"]
      interval: 30s
      timeout: 10s
      retries: 5

  kafka-ui:
    image: provectuslabs/kafka-ui:latest
    container_name: kafka-ui
    restart: unless-stopped
    depends_on:
      kafka:
        condition: service_healthy
    environment:
      KAFKA_CLUSTERS_0_NAME: local
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: kafka:29092
      KAFKA_CLUSTERS_0_ZOOKEEPER: zookeeper:2181
    ports:
      - "8090:8080"
    networks:
      - coupon-network

  # Shared Jaeger (moved from individual services)
  jaeger:
    image: jaegertracing/all-in-one:1.56
    container_name: jaeger
    restart: unless-stopped
    ports:
      - "16686:16686"
      - "6831:6831/udp"
    networks:
      - coupon-network

  # Shared Adminer for database management
  adminer:
    image: adminer:4.8.1
    container_name: adminer
    restart: unless-stopped
    ports:
      - "8091:8080"
    networks:
      - coupon-network

volumes:
  zookeeper-data:
  zookeeper-logs:
  kafka-data:

networks:
  coupon-network:
    name: coupon-network
    driver: bridge
```

## Topic Creation Script

### create-topics.sh
```bash
#!/bin/bash

KAFKA_CONTAINER="kafka"
BOOTSTRAP_SERVER="kafka:29092"

echo "Waiting for Kafka to be ready..."
docker exec $KAFKA_CONTAINER kafka-topics --bootstrap-server $BOOTSTRAP_SERVER --list

echo "Creating Kafka topics..."

# Voucher Events Topic
docker exec $KAFKA_CONTAINER kafka-topics \
  --create \
  --bootstrap-server $BOOTSTRAP_SERVER \
  --topic voucher-events \
  --partitions 3 \
  --replication-factor 1 \
  --config retention.ms=604800000 \
  --config segment.ms=86400000

# Order Events Topic
docker exec $KAFKA_CONTAINER kafka-topics \
  --create \
  --bootstrap-server $BOOTSTRAP_SERVER \
  --topic order-events \
  --partitions 3 \
  --replication-factor 1 \
  --config retention.ms=604800000 \
  --config segment.ms=86400000

# User Events Topic
docker exec $KAFKA_CONTAINER kafka-topics \
  --create \
  --bootstrap-server $BOOTSTRAP_SERVER \
  --topic user-events \
  --partitions 3 \
  --replication-factor 1 \
  --config retention.ms=604800000 \
  --config segment.ms=86400000

# Notification Events Topic
docker exec $KAFKA_CONTAINER kafka-topics \
  --create \
  --bootstrap-server $BOOTSTRAP_SERVER \
  --topic notification-events \
  --partitions 3 \
  --replication-factor 1 \
  --config retention.ms=259200000 \
  --config segment.ms=86400000

echo "Topics created successfully!"

# List all topics to verify
echo "Current topics:"
docker exec $KAFKA_CONTAINER kafka-topics --bootstrap-server $BOOTSTRAP_SERVER --list
```

## Service Configuration Updates

### Update shared-libs config for Kafka topics
```go
// coupon-shared-libs/config/config.go
type KafkaTopicsConfig struct {
    VoucherEvents      string `mapstructure:"voucher_events"`
    OrderEvents        string `mapstructure:"order_events"`
    UserEvents         string `mapstructure:"user_events"`
    NotificationEvents string `mapstructure:"notification_events"`
}

// Set defaults in Load function
v.SetDefault("kafka.topics.voucher_events", "voucher-events")
v.SetDefault("kafka.topics.order_events", "order-events")
v.SetDefault("kafka.topics.user_events", "user-events")
v.SetDefault("kafka.topics.notification_events", "notification-events")
```

### Voucher Service Kafka Configuration
```yaml
# coupon-voucher-service/config/config.yaml
kafka:
  brokers: ["kafka:29092"]
  group_id: "voucher-service"
  topics:
    voucher_events: "voucher-events"
```

### Order Service Kafka Configuration
```yaml
# coupon-order-service/config/config.yaml
kafka:
  brokers: ["kafka:29092"]
  group_id: "order-service"
  topics:
    order_events: "order-events"
```

### User Service Kafka Configuration
```yaml
# coupon-user-service/config/config.yaml
kafka:
  brokers: ["kafka:29092"]
  group_id: "user-service"
  topics:
    user_events: "user-events"
```

### Notification Service Kafka Configuration
```yaml
# coupon-notification-service/config/config.yaml
kafka:
  brokers: ["kafka:29092"]
  group_id: "notification-service"
  topics:
    voucher_events: "voucher-events"
    order_events: "order-events"
    user_events: "user-events"
    notification_events: "notification-events"
```

## Event Publishing Helper

### Shared Event Publisher
```go
// coupon-shared-libs/kafka/event_publisher.go
package kafka

import (
    "context"
    "encoding/json"
    "fmt"
    "time"

    "github.com/google/uuid"
    "gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
    "gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
)

type EventPublisher struct {
    producer *Producer
    logger   *logging.Logger
    config   *config.KafkaConfig
}

type BaseEvent struct {
    EventID   string    `json:"event_id"`
    EventType string    `json:"event_type"`
    Timestamp time.Time `json:"timestamp"`
    ServiceName string  `json:"service_name"`
    Version   string    `json:"version"`
}

func NewEventPublisher(cfg *config.KafkaConfig, logger *logging.Logger) *EventPublisher {
    producer := NewProducer(cfg, logger)
    return &EventPublisher{
        producer: producer,
        logger:   logger,
        config:   cfg,
    }
}

func (ep *EventPublisher) PublishEvent(ctx context.Context, topic string, eventType string, payload interface{}, key string) error {
    event := map[string]interface{}{
        "event_id":     uuid.New().String(),
        "event_type":   eventType,
        "timestamp":    time.Now(),
        "service_name": "unknown", // Should be injected by service
        "version":      "1.0.0",
        "payload":      payload,
    }

    message := &Message{
        Key:       key,
        Value:     event,
        Headers:   map[string]string{
            "event-type": eventType,
            "content-type": "application/json",
        },
        Timestamp: time.Now(),
    }

    return ep.producer.SendMessage(ctx, topic, message)
}

func (ep *EventPublisher) Close() error {
    return ep.producer.Close()
}
```

## Monitoring and Observability

### Kafka Metrics Configuration
```yaml
# Add to each service config
metrics:
  kafka:
    enabled: true
    consumer_lag_threshold: 1000
    error_rate_threshold: 0.05
```

### Health Check Integration
```go
// Add Kafka health check to existing health checker
func (hc *HealthChecker) CheckKafka(brokers []string) error {
    // Implementation to check Kafka connectivity
    // Return error if Kafka is not accessible
}
```

## Deployment Strategy

### 1. Infrastructure First
```bash
# Start shared infrastructure
docker-compose -f infrastructure-compose.yml up -d

# Wait for services to be healthy
./wait-for-services.sh

# Create topics
./create-topics.sh
```

### 2. Service Updates
```bash
# Update existing services with Kafka publishing
# Deploy notification service
# Verify event flow through Kafka UI
```

### 3. Monitoring Setup
```bash
# Set up Kafka monitoring dashboards
# Configure alerting for consumer lag
# Set up log aggregation for event processing
```

This infrastructure setup provides:
- Shared Kafka and Zookeeper for all services
- Topic management with proper retention policies
- Monitoring through Kafka UI
- Shared Jaeger and Adminer instances
- Proper health checks and service dependencies
- Configuration templates for all services
