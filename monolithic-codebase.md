Project Path: coupon-be

Source Tree:

```txt
coupon-be
├── assets
├── cmd
│   └── api
│       ├── main.go
│       └── server
│           └── init.go
├── go.mod
├── internal
│   ├── auth
│   │   ├── handler.go
│   │   └── route.go
│   ├── coupon
│   │   ├── handlers.go
│   │   └── route.go
│   ├── middleware
│   │   ├── require_roles.go
│   │   └── verify_user.go
│   ├── order
│   │   ├── handler.go
│   │   └── route.go
│   ├── product
│   │   ├── handler.go
│   │   └── route.go
│   └── user
│       ├── handler.go
│       └── route.go
├── migrations
│   ├── check_coupon_eligibility.sql
│   └── ddl.sql
└── pkg
    ├── db
    │   ├── models
    │   │   ├── coupon.go
    │   │   ├── order.go
    │   │   ├── product.go
    │   │   └── user.go
    │   └── postgresql
    │       └── init.go
    ├── repository
    │   ├── coupon_repository.go
    │   ├── order_repository.go
    │   ├── product_repository.go
    │   └── user_repository.go
    ├── service
    │   ├── auth_service.go
    │   ├── coupon_service.go
    │   ├── order_service.go
    │   ├── product_service.go
    │   └── user_service.go
    ├── testutils
    └── utils
        ├── handle_error.go
        ├── token.go
        └── validate.go

```

`coupon-be/cmd/api/main.go`:

```go
package main

import (
	"coupon/cmd/api/server"

	"github.com/rs/zerolog/log"
)

func main() {
	s := server.New()
	err := s.Start(":8080")
	if err != nil {
		log.Err(err).Send()
	}
}

```

`coupon-be/cmd/api/server/init.go`:

```go
package server

import (
	"os"

	"coupon/internal/auth"
	"coupon/internal/coupon"
	"coupon/internal/middleware"
	"coupon/internal/order"
	"coupon/internal/product"
	"coupon/internal/user"
	"coupon/pkg/db/postgresql"
	"coupon/pkg/repository"
	"coupon/pkg/utils"

	echoAdapter "github.com/TickLabVN/tonic/adapters/echo"
	"github.com/TickLabVN/tonic/core/docs"
	"github.com/joho/godotenv"
	"github.com/labstack/echo/v4"
	echomiddleware "github.com/labstack/echo/v4/middleware"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
)

func New() *echo.Echo {
	godotenv.Load()

	zerolog.SetGlobalLevel(zerolog.TraceLevel)
	log.Logger = log.Output(zerolog.ConsoleWriter{
		Out: os.Stdout,
	})

	db := postgresql.InitDB()

	couponRepo := repository.NewCouponRepository(db)
	orderRepo := repository.NewOrderRepository(db)
	userRepo := repository.NewUserRepository(db)
	productRepo := repository.NewProductRepository(db)

	e := echo.New()
	e.HTTPErrorHandler = utils.HTTPErrorHandler
	e.Use(echomiddleware.CORSWithConfig(echomiddleware.CORSConfig{
		AllowOrigins:     []string{"http://localhost:3000"},
		AllowCredentials: true,
	}))

	openapi := &docs.OpenApi{
		OpenAPI: "3.0.1",
		Info: docs.InfoObject{
			Title:   "Coupon Management API",
			Version: "1.0.0",
		},
		Tags: []docs.Tag{
			{Name: "Auth API"},
			{Name: "Users API"},
			{Name: "Coupons API"},
			{Name: "Orders API"},
			{Name: "Products API"},
		},
	}

	echoAdapter.UIHandle(e, openapi, "/docs")

	e.Use(middleware.VerifyUser)

	auth.SetupRoutes(e, openapi, userRepo)
	user.SetupRoutes(e, openapi, userRepo)
	coupon.SetupRoutes(e, openapi, db, couponRepo)
	product.SetupRoutes(e, openapi, productRepo)
	order.SetupRoutes(e, openapi, db, orderRepo, couponRepo)

	return e
}

```

`coupon-be/go.mod`:

```mod
module coupon

go 1.24.4

require (
	github.com/DATA-DOG/go-sqlmock v1.5.2
	github.com/TickLabVN/tonic/adapters/echo v0.0.0-**************-005921cb9127
	github.com/TickLabVN/tonic/core v0.0.0-**************-005921cb9127
	github.com/joho/godotenv v1.5.1
	github.com/labstack/echo/v4 v4.13.4
	github.com/lib/pq v1.10.9
	github.com/rs/zerolog v1.34.0
	github.com/stretchr/testify v1.10.0
	golang.org/x/crypto v0.39.0
)

require (
	github.com/KyleBanks/depth v1.2.1 // indirect
	github.com/davecgh/go-spew v1.1.1 // indirect
	github.com/go-openapi/jsonpointer v0.21.1 // indirect
	github.com/go-openapi/jsonreference v0.21.0 // indirect
	github.com/go-openapi/spec v0.21.0 // indirect
	github.com/go-openapi/swag v0.23.1 // indirect
	github.com/josharian/intern v1.0.0 // indirect
	github.com/labstack/gommon v0.4.2 // indirect
	github.com/mailru/easyjson v0.9.0 // indirect
	github.com/mattn/go-colorable v0.1.14 // indirect
	github.com/mattn/go-isatty v0.0.20 // indirect
	github.com/pmezard/go-difflib v1.0.0 // indirect
	github.com/stretchr/objx v0.5.2 // indirect
	github.com/swaggo/files v1.0.1 // indirect
	github.com/swaggo/http-swagger v1.3.4 // indirect
	github.com/swaggo/swag v1.16.4 // indirect
	github.com/valyala/bytebufferpool v1.0.0 // indirect
	github.com/valyala/fasttemplate v1.2.2 // indirect
	golang.org/x/net v0.41.0 // indirect
	golang.org/x/sys v0.33.0 // indirect
	golang.org/x/text v0.26.0 // indirect
	golang.org/x/time v0.11.0 // indirect
	golang.org/x/tools v0.34.0 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
)

```

`coupon-be/internal/auth/handler.go`:

```go
package auth

import (
	"net/http"
	"os"
	"time"

	"coupon/pkg/db/models"
	"coupon/pkg/service"
	"coupon/pkg/utils"

	echoAdapter "github.com/TickLabVN/tonic/adapters/echo"
	"github.com/TickLabVN/tonic/core/docs"
	"github.com/labstack/echo/v4"
)

type AuthHandler struct {
	service service.AuthService
}

type authResponse struct {
	ID       int             `json:"id"`
	Email    string          `json:"email"`
	FullName string          `json:"full_name"`
	Role     models.UserRole `json:"role"`
	Type     models.UserType `json:"type"`
}

type messageResponse struct {
	Message string `json:"message"`
}

func NewAuthHandler(s service.AuthService) *AuthHandler {
	return &AuthHandler{service: s}
}

func (h *AuthHandler) RegisterRoutes(e *echo.Echo, spec *docs.OpenApi) {
	api := e.Group("/api")

	signupRoute := api.POST("/signup", h.Signup)
	echoAdapter.AddRoute[models.SignupRequest, authResponse](spec, signupRoute, docs.OperationObject{Tags: []string{"Auth API"}})

	loginRoute := api.POST("/login", h.Login)
	echoAdapter.AddRoute[models.LoginRequest, authResponse](spec, loginRoute, docs.OperationObject{Tags: []string{"Auth API"}})

	logoutRoute := api.POST("/logout", h.Logout)
	echoAdapter.AddRoute[struct{}, messageResponse](spec, logoutRoute, docs.OperationObject{Tags: []string{"Auth API"}})
}

func (h *AuthHandler) Login(c echo.Context) error {
	var req models.LoginRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, &utils.ValidationError{Message: "Invalid request"})
	}
	if err := utils.ValidateStruct(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err)
	}

	user, err := h.service.Login(&req)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, &utils.ValidationError{Message: err.Error()})
	}

	secret := os.Getenv("JWT_SECRET")
	if secret == "" {
		return echo.NewHTTPError(http.StatusInternalServerError, "jwt secret not configured")
	}

	token, err := utils.GenerateToken(user.ID, string(user.Role), secret)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "failed to sign token")
	}

	cookie := &http.Cookie{
		Name:     "token",
		Value:    token,
		Path:     "/",
		HttpOnly: true,
		Expires:  time.Now().Add(24 * time.Hour),
	}
	c.SetCookie(cookie)

	resp := authResponse{
		ID:       user.ID,
		Email:    user.Email,
		FullName: user.FullName,
		Role:     user.Role,
		Type:     user.Type,
	}

	return c.JSON(http.StatusOK, resp)
}

func (h *AuthHandler) Signup(c echo.Context) error {
	var req models.SignupRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, &utils.ValidationError{Message: "Invalid request"})
	}
	if err := utils.ValidateStruct(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err)
	}

	user, err := h.service.Signup(&req)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, &utils.ValidationError{Message: err.Error()})
	}

	secret := os.Getenv("JWT_SECRET")
	if secret == "" {
		return echo.NewHTTPError(http.StatusInternalServerError, "jwt secret not configured")
	}

	token, err := utils.GenerateToken(user.ID, string(user.Role), secret)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "failed to sign token")
	}

	cookie := &http.Cookie{
		Name:     "token",
		Value:    token,
		Path:     "/",
		HttpOnly: true,
		Expires:  time.Now().Add(24 * time.Hour),
	}
	c.SetCookie(cookie)

	resp := authResponse{
		ID:       user.ID,
		Email:    user.Email,
		FullName: user.FullName,
		Role:     user.Role,
		Type:     user.Type,
	}

	return c.JSON(http.StatusCreated, resp)
}

func (h *AuthHandler) Logout(c echo.Context) error {
	cookie := &http.Cookie{
		Name:     "token",
		Value:    "",
		Path:     "/",
		HttpOnly: true,
		Expires:  time.Unix(0, 0),
	}
	c.SetCookie(cookie)

	return c.JSON(http.StatusOK, messageResponse{Message: "logged out"})
}

```

`coupon-be/internal/auth/route.go`:

```go
package auth

import (
	"coupon/pkg/repository"
	"coupon/pkg/service"

	"github.com/TickLabVN/tonic/core/docs"
	"github.com/labstack/echo/v4"
)

func SetupRoutes(e *echo.Echo, spec *docs.OpenApi, repo repository.UserRepository) {
	svc := service.NewAuthService(repo)
	handler := NewAuthHandler(svc)
	handler.RegisterRoutes(e, spec)
}

```

`coupon-be/internal/coupon/handlers.go`:

```go
package coupon

import (
	"net/http"
	"strconv"
	"strings"

	"coupon/internal/middleware"
	"coupon/pkg/db/models"
	"coupon/pkg/service"
	"coupon/pkg/utils"

	echoAdapter "github.com/TickLabVN/tonic/adapters/echo"
	"github.com/TickLabVN/tonic/core/docs"
	"github.com/labstack/echo/v4"
	"github.com/rs/zerolog/log"
)

type CouponHandler struct {
	couponService service.CouponService
}

type couponIDParams struct {
	ID int `param:"id" validate:"required"`
}

type UpdateCouponRequestWithID struct {
	ID                         int `param:"id" validate:"required"`
	models.UpdateCouponRequest `json:",inline"`
}

type messageResponse struct {
	Message string `json:"message"`
}

type DiscountTypeList []models.DiscountType

type EligibleCouponList []models.EligibleCoupon

func NewCouponHandler(couponSvc service.CouponService) *CouponHandler {
	return &CouponHandler{couponService: couponSvc}
}

func (h *CouponHandler) RegisterRoutes(e *echo.Echo, spec *docs.OpenApi) {
	api := e.Group("/api")
	admin := e.Group("/api", middleware.RequireRoles(string(models.UserRoleAdmin)))

	r := admin.POST("/coupons", h.CreateCoupon)
	echoAdapter.AddRoute[models.CreateCouponRequest, models.Coupon](spec, r, docs.OperationObject{Tags: []string{"Coupons API"}})

	r = admin.GET("/coupons", h.ListCoupons)
	echoAdapter.AddRoute[models.ListCouponsRequest, models.PaginatedResponse[*models.Coupon]](spec, r, docs.OperationObject{Tags: []string{"Coupons API"}})

	r = admin.GET("/coupons/:id", h.GetCouponByID)
	echoAdapter.AddRoute[couponIDParams, models.Coupon](spec, r, docs.OperationObject{Tags: []string{"Coupons API"}})

	r = admin.PUT("/coupons/:id", h.UpdateCoupon)
	echoAdapter.AddRoute[UpdateCouponRequestWithID, models.Coupon](spec, r, docs.OperationObject{
		Tags:        []string{"Coupons API"},
		Description: "Update coupon. Editing is not allowed once the coupon has been used at least once.",
	})

	r = api.GET("/discount-types", h.GetDiscountTypes)
	echoAdapter.AddRoute[struct{}, DiscountTypeList](spec, r, docs.OperationObject{Tags: []string{"Coupons API"}})

	r = api.POST("/coupons/check-eligibility", h.CheckCouponEligibility)
	echoAdapter.AddRoute[models.CouponEligibilityRequest, models.EligibleCoupon](spec, r, docs.OperationObject{Tags: []string{"Coupons API"}})

	r = api.POST("/coupons/eligible-auto", h.ListEligibleAutoCoupons)
	echoAdapter.AddRoute[models.AutoCouponEligibilityRequest, EligibleCouponList](spec, r, docs.OperationObject{Tags: []string{"Coupons API"}})
}

func (h *CouponHandler) CreateCoupon(c echo.Context) error {
	var req models.CreateCouponRequest
	if err := c.Bind(&req); err != nil {
		log.Error().Err(err).Msg("Failed to bind create coupon request")
		return echo.NewHTTPError(http.StatusBadRequest, &utils.ValidationError{
			Message: "Invalid request format",
			Metadata: map[string]any{
				"error": err.Error(),
			},
		})
	}

	if err := utils.ValidateStruct(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err)
	}

	userID, ok := c.Get("userID").(int)
	if !ok {
		log.Error().Msg("User ID not found in context")
		return echo.NewHTTPError(http.StatusUnauthorized, "User not authenticated")
	}

	coupon, err := h.couponService.CreateCoupon(&req, userID)
	if err != nil {
		log.Error().Err(err).Msg("Failed to create coupon")
		if validationErr, ok := err.(*utils.ValidationError); ok {
			return echo.NewHTTPError(http.StatusBadRequest, validationErr)
		}

		return echo.NewHTTPError(http.StatusInternalServerError, &utils.ValidationError{
			Message: "Failed to create coupon",
			Metadata: map[string]any{
				"error": err.Error(),
			},
		})
	}

	log.Info().Int("coupon_id", coupon.ID).Str("coupon_code", coupon.CouponCode).Msg("Coupon created successfully")
	return c.JSON(http.StatusCreated, map[string]any{
		"message": "Coupon created successfully",
		"data":    coupon,
	})
}

func (h *CouponHandler) GetCouponByID(c echo.Context) error {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, &utils.ValidationError{
			Message: "Invalid coupon ID",
			Metadata: map[string]any{
				"field": "id",
				"value": idStr,
			},
		})
	}

	coupon, err := h.couponService.GetCouponByID(id)
	if err != nil {
		log.Error().Err(err).Int("coupon_id", id).Msg("Failed to get coupon")
		return echo.NewHTTPError(http.StatusNotFound, &utils.ValidationError{
			Message: "Coupon not found",
			Metadata: map[string]any{
				"coupon_id": id,
			},
		})
	}

	return c.JSON(http.StatusOK, coupon)
}

func (h *CouponHandler) UpdateCoupon(c echo.Context) error {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, &utils.ValidationError{
			Message: "Invalid coupon ID",
			Metadata: map[string]any{
				"field": "id",
				"value": idStr,
			},
		})
	}

	var req models.UpdateCouponRequest
	if err := c.Bind(&req); err != nil {
		log.Error().Err(err).Msg("Failed to bind update coupon request")
		return echo.NewHTTPError(http.StatusBadRequest, &utils.ValidationError{
			Message: "Invalid request format",
			Metadata: map[string]any{
				"error": err.Error(),
			},
		})
	}

	if err := utils.ValidateStruct(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err)
	}

	coupon, err := h.couponService.UpdateCoupon(id, &req)
	if err != nil {
		log.Error().Err(err).Int("coupon_id", id).Msg("Failed to update coupon")
		if validationErr, ok := err.(*utils.ValidationError); ok {
			return echo.NewHTTPError(http.StatusBadRequest, validationErr)
		}
		return echo.NewHTTPError(http.StatusNotFound, &utils.ValidationError{
			Message: "Coupon not found",
			Metadata: map[string]any{
				"coupon_id": id,
			},
		})
	}

	log.Info().Int("coupon_id", id).Msg("Coupon updated successfully")
	return c.JSON(http.StatusOK, map[string]any{
		"message": "Coupon updated successfully",
		"coupon":  coupon,
	})
}

func (h *CouponHandler) ListCoupons(c echo.Context) error {
	var req models.ListCouponsRequest

	if pageStr := c.QueryParam("page"); pageStr != "" {
		page, err := strconv.Atoi(pageStr)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, &utils.ValidationError{
				Message: "Invalid query parameters",
				Metadata: map[string]any{
					"field": "page",
					"value": pageStr,
				},
			})
		}
		req.Page = page
	}

	if limitStr := c.QueryParam("limit"); limitStr != "" {
		limit, err := strconv.Atoi(limitStr)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, &utils.ValidationError{
				Message: "Invalid query parameters",
				Metadata: map[string]any{
					"field": "limit",
					"value": limitStr,
				},
			})
		}
		req.Limit = limit
	}

	req.Search = c.QueryParam("search")

	if dtID := c.QueryParam("discount_type_id"); dtID != "" {
		val, err := strconv.Atoi(dtID)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, &utils.ValidationError{
				Message: "Invalid query parameters",
				Metadata: map[string]any{
					"field": "discount_type_id",
					"value": dtID,
				},
			})
		}
		req.DiscountTypeID = &val
	}

	if um := c.QueryParam("usage_method"); um != "" {
		method := models.UsageMethod(strings.ToUpper(um))
		req.UsageMethod = &method
	}

	req.Status = c.QueryParam("status")
	req.SortBy = c.QueryParam("sort_by")
	req.SortOrder = c.QueryParam("sort_order")

	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Limit <= 0 {
		req.Limit = 10
	}
	if req.SortBy == "" {
		req.SortBy = "created_at"
	}
	if req.SortOrder == "" {
		req.SortOrder = "desc"
	}

	response, err := h.couponService.ListCoupons(&req)
	if err != nil {
		log.Error().Err(err).Msg("Failed to list coupons")
		return echo.NewHTTPError(http.StatusInternalServerError, &utils.ValidationError{
			Message: "Failed to retrieve coupons",
			Metadata: map[string]any{
				"error": err.Error(),
			},
		})
	}

	return c.JSON(http.StatusOK, response)
}

func (h *CouponHandler) GetDiscountTypes(c echo.Context) error {
	discountTypes, err := h.couponService.GetDiscountTypes()
	if err != nil {
		log.Error().Err(err).Msg("Failed to get discount types")
		return echo.NewHTTPError(http.StatusInternalServerError, &utils.ValidationError{
			Message: "Failed to retrieve discount types",
			Metadata: map[string]any{
				"error": err.Error(),
			},
		})
	}

	return c.JSON(http.StatusOK, discountTypes)
}

func (h *CouponHandler) CheckCouponEligibility(c echo.Context) error {
	var req models.CouponEligibilityRequest
	if err := c.Bind(&req); err != nil {
		log.Error().Err(err).Msg("Failed to bind eligibility request")
		return echo.NewHTTPError(http.StatusBadRequest, &utils.ValidationError{
			Message:  "Invalid request format",
			Metadata: map[string]any{"error": err.Error()},
		})
	}

	if err := utils.ValidateStruct(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err)
	}

	resp, err := h.couponService.CheckCouponEligibility(&req)
	if err != nil {
		log.Error().Err(err).Msg("Failed to check coupon eligibility")
		if validationErr, ok := err.(*utils.ValidationError); ok {
			return echo.NewHTTPError(http.StatusBadRequest, validationErr)
		}
		return echo.NewHTTPError(http.StatusInternalServerError, &utils.ValidationError{
			Message:  "Failed to check coupon eligibility",
			Metadata: map[string]any{"error": err.Error()},
		})
	}

	if resp.Eligible && resp.CouponID != nil {
		coupon, err := h.couponService.GetCouponByID(*resp.CouponID)
		if err != nil {
			log.Error().Err(err).Msg("Failed to get coupon details")
			return echo.NewHTTPError(http.StatusInternalServerError, &utils.ValidationError{
				Message:  "Failed to retrieve coupon details",
				Metadata: map[string]any{"error": err.Error()},
			})
		}

		return c.JSON(http.StatusOK, &models.EligibleCoupon{
			Eligible:       resp.Eligible,
			Coupon:         coupon,
			DiscountAmount: resp.DiscountAmount,
		})
	}

	return c.JSON(http.StatusOK, resp)
}

func (h *CouponHandler) ListEligibleAutoCoupons(c echo.Context) error {
	var req models.AutoCouponEligibilityRequest
	if err := c.Bind(&req); err != nil {
		log.Error().Err(err).Msg("Failed to bind auto coupon request")
		return echo.NewHTTPError(http.StatusBadRequest, &utils.ValidationError{
			Message:  "Invalid request format",
			Metadata: map[string]any{"error": err.Error()},
		})
	}

	if err := utils.ValidateStruct(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err)
	}

	coupons, err := h.couponService.ListEligibleAutoCoupons(&req)
	if err != nil {
		log.Error().Err(err).Msg("Failed to list eligible auto coupons")
		if validationErr, ok := err.(*utils.ValidationError); ok {
			return echo.NewHTTPError(http.StatusBadRequest, validationErr)
		}
		return echo.NewHTTPError(http.StatusInternalServerError, &utils.ValidationError{
			Message:  "Failed to get eligible coupons",
			Metadata: map[string]any{"error": err.Error()},
		})
	}

	return c.JSON(http.StatusOK, coupons)
}

```

`coupon-be/internal/coupon/route.go`:

```go
package coupon

import (
	"database/sql"

	"coupon/pkg/repository"
	"coupon/pkg/service"

	"github.com/TickLabVN/tonic/core/docs"
	"github.com/labstack/echo/v4"
)

func SetupRoutes(e *echo.Echo, spec *docs.OpenApi, db *sql.DB, repo repository.CouponRepository) {
	svc := service.NewCouponService(db, repo)
	handler := NewCouponHandler(svc)
	handler.RegisterRoutes(e, spec)
}

```

`coupon-be/internal/middleware/require_roles.go`:

```go
package middleware

import (
	"slices"

	"github.com/labstack/echo/v4"
)

func RequireRoles(allowed ...string) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			r, ok := c.Get("role").(string)
			if !ok {
				return echo.ErrForbidden
			}
			if slices.Contains(allowed, r) {
				return next(c)
			}
			return echo.ErrForbidden
		}
	}
}

```

`coupon-be/internal/middleware/verify_user.go`:

```go
package middleware

import (
	"os"
	"strings"

	"coupon/pkg/utils"

	"github.com/labstack/echo/v4"
)

func VerifyUser(next echo.HandlerFunc) echo.HandlerFunc {
	return func(c echo.Context) error {
		path := c.Request().URL.Path
		if strings.HasPrefix(path, "/docs") || path == "/api/login" || path == "/api/logout" || path == "/api/signup" {
			return next(c)
		}

		cookie, err := c.Cookie("token")
		if err != nil {
			return echo.ErrUnauthorized
		}

		secret := os.Getenv("JWT_SECRET")
		if secret == "" {
			return echo.ErrUnauthorized
		}

		userID, role, err := utils.ParseToken(cookie.Value, secret)
		if err != nil {
			c.Logger().Info(err)
			return echo.ErrForbidden
		}

		c.Set("userID", userID)
		c.Set("role", role)
		return next(c)
	}
}

```

`coupon-be/internal/order/handler.go`:

```go
package order

import (
	"math"
	"net/http"
	"strconv"

	"coupon/internal/middleware"
	"coupon/pkg/db/models"
	"coupon/pkg/service"
	"coupon/pkg/utils"

	echoAdapter "github.com/TickLabVN/tonic/adapters/echo"
	"github.com/TickLabVN/tonic/core/docs"
	"github.com/labstack/echo/v4"
	"github.com/rs/zerolog/log"
)

type OrderHandler struct {
	service service.OrderService
}

func NewOrderHandler(s service.OrderService) *OrderHandler {
	return &OrderHandler{service: s}
}

type orderIDParam struct {
	ID int `param:"id" validate:"required"`
}

type listOrdersQuery struct {
	Page   int    `json:"page" query:"page"`
	Limit  int    `json:"limit" query:"limit"`
	Search string `json:"search" query:"search"`
}

func (h *OrderHandler) RegisterRoutes(e *echo.Echo, spec *docs.OpenApi) {
	api := e.Group("/api")
	admin := e.Group("/api", middleware.RequireRoles(string(models.UserRoleAdmin)))

	r := api.POST("/orders", h.CreateOrder)
	echoAdapter.AddRoute[models.OrderCalculationRequest, models.Order](spec, r, docs.OperationObject{Tags: []string{"Orders API"}})

	r = admin.GET("/orders", h.ListOrders)
	echoAdapter.AddRoute[listOrdersQuery, models.PaginatedResponse[models.Order]](spec, r, docs.OperationObject{Tags: []string{"Orders API"}})

	r = api.GET("/orders/:id", h.GetOrderByID)
	echoAdapter.AddRoute[orderIDParam, models.Order](spec, r, docs.OperationObject{Tags: []string{"Orders API"}})

	r = admin.GET("/coupons/:id/orders", h.ListOrdersByCoupon)
	echoAdapter.AddRoute[struct {
		orderIDParam
		listOrdersQuery
	}, models.PaginatedResponse[models.Order]](spec, r, docs.OperationObject{Tags: []string{"Orders API"}})

	r = api.POST("/orders/calculate", h.CalculateOrderDiscount)
	echoAdapter.AddRoute[models.OrderCalculationRequest, models.OrderCalculationResponse](spec, r, docs.OperationObject{Tags: []string{"Orders API"}})
}

func (h *OrderHandler) CreateOrder(c echo.Context) error {
	var req models.OrderCalculationRequest
	if err := c.Bind(&req); err != nil {
		log.Error().Err(err).Msg("Failed to bind create order request")
		return echo.NewHTTPError(http.StatusBadRequest, &utils.ValidationError{
			Message:  "Invalid request format",
			Metadata: map[string]any{"error": err.Error()},
		})
	}

	if err := utils.ValidateStruct(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err)
	}

	if userID, ok := c.Get("userID").(int); ok {
		req.UserID = userID
	}

	order, err := h.service.CreateOrder(&req)
	if err != nil {
		log.Error().Err(err).Msg("Failed to create order")
		return echo.NewHTTPError(http.StatusInternalServerError, &utils.ValidationError{
			Message:  "Failed to create order",
			Metadata: map[string]any{"error": err.Error()},
		})
	}

	return c.JSON(http.StatusCreated, order)
}

func (h *OrderHandler) GetOrderByID(c echo.Context) error {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, &utils.ValidationError{
			Message:  "Invalid order ID",
			Metadata: map[string]any{"field": "id", "value": idStr},
		})
	}

	order, err := h.service.GetOrderByID(id)
	if err != nil {
		log.Error().Err(err).Int("order_id", id).Msg("Failed to get order")
		return echo.NewHTTPError(http.StatusNotFound, &utils.ValidationError{
			Message:  "Order not found",
			Metadata: map[string]any{"order_id": id},
		})
	}

	role, _ := c.Get("role").(string)
	userID, _ := c.Get("userID").(int)
	if role != string(models.UserRoleAdmin) && order.UserID != userID {
		return echo.ErrForbidden
	}

	return c.JSON(http.StatusOK, order)
}

func (h *OrderHandler) ListOrders(c echo.Context) error {
	page, _ := strconv.Atoi(c.QueryParam("page"))
	limit, _ := strconv.Atoi(c.QueryParam("limit"))
	search := c.QueryParam("search")

	if page <= 0 {
		page = 1
	}
	if limit <= 0 {
		limit = 10
	}

	orders, total, err := h.service.ListOrders(page, limit, search)
	if err != nil {
		log.Error().Err(err).Msg("Failed to list orders")
		return echo.NewHTTPError(http.StatusInternalServerError, &utils.ValidationError{
			Message:  "Failed to list orders",
			Metadata: map[string]any{"error": err.Error()},
		})
	}

	totalPages := int(math.Ceil(float64(total) / float64(limit)))
	resp := &models.PaginatedResponse[models.Order]{
		Data:       orders,
		Total:      total,
		Page:       page,
		Limit:      limit,
		TotalPages: totalPages,
	}

	return c.JSON(http.StatusOK, resp)
}

func (h *OrderHandler) ListOrdersByCoupon(c echo.Context) error {
	idStr := c.Param("id")
	couponID, err := strconv.Atoi(idStr)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, &utils.ValidationError{
			Message:  "Invalid coupon ID",
			Metadata: map[string]any{"field": "id", "value": idStr},
		})
	}

	page, _ := strconv.Atoi(c.QueryParam("page"))
	limit, _ := strconv.Atoi(c.QueryParam("limit"))
	if page <= 0 {
		page = 1
	}
	if limit <= 0 {
		limit = 10
	}

	orders, total, err := h.service.ListOrdersByCoupon(couponID, page, limit)
	if err != nil {
		log.Error().Err(err).Msg("Failed to list orders by coupon")
		return echo.NewHTTPError(http.StatusInternalServerError, &utils.ValidationError{
			Message:  "Failed to list orders",
			Metadata: map[string]any{"error": err.Error()},
		})
	}

	totalPages := int(math.Ceil(float64(total) / float64(limit)))
	resp := &models.PaginatedResponse[models.Order]{
		Data:       orders,
		Total:      total,
		Page:       page,
		Limit:      limit,
		TotalPages: totalPages,
	}

	return c.JSON(http.StatusOK, resp)
}

func (h *OrderHandler) CalculateOrderDiscount(c echo.Context) error {
	var req models.OrderCalculationRequest
	if err := c.Bind(&req); err != nil {
		log.Error().Err(err).Msg("Failed to bind order calculation request")
		return echo.NewHTTPError(http.StatusBadRequest, &utils.ValidationError{
			Message:  "Invalid request format",
			Metadata: map[string]any{"error": err.Error()},
		})
	}

	if err := utils.ValidateStruct(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err)
	}

	if userID, ok := c.Get("userID").(int); ok {
		req.UserID = userID
	}

	resp, err := h.service.CalculateDiscount(&req)
	if err != nil {
		log.Error().Err(err).Msg("Failed to calculate order discount")
		return echo.NewHTTPError(http.StatusInternalServerError, &utils.ValidationError{
			Message:  "Failed to calculate order discount",
			Metadata: map[string]any{"error": err.Error()},
		})
	}

	log.Info().
		Float64("order_amount", req.OrderAmount).
		Interface("coupon_code", req.CouponCode).
		Float64("discount_amount", resp.DiscountAmount).
		Float64("final_amount", resp.FinalAmount).
		Str("status", resp.Status).
		Msg("Order discount calculated")

	return c.JSON(http.StatusOK, resp)
}

```

`coupon-be/internal/order/route.go`:

```go
package order

import (
	"database/sql"

	"coupon/pkg/repository"
	"coupon/pkg/service"

	"github.com/TickLabVN/tonic/core/docs"
	"github.com/labstack/echo/v4"
)

func SetupRoutes(e *echo.Echo, spec *docs.OpenApi, db *sql.DB, orderRepo repository.OrderRepository, couponRepo repository.CouponRepository) {
	svc := service.NewOrderService(db, orderRepo, couponRepo)
	handler := NewOrderHandler(svc)
	handler.RegisterRoutes(e, spec)
}

```

`coupon-be/internal/product/handler.go`:

```go
package product

import (
	"math"
	"net/http"
	"strconv"

	"coupon/internal/middleware"
	"coupon/pkg/db/models"
	"coupon/pkg/service"
	"coupon/pkg/utils"

	echoAdapter "github.com/TickLabVN/tonic/adapters/echo"
	"github.com/TickLabVN/tonic/core/docs"
	"github.com/labstack/echo/v4"
	"github.com/rs/zerolog/log"
)

type ProductHandler struct {
	service service.ProductService
}

func NewProductHandler(s service.ProductService) *ProductHandler {
	return &ProductHandler{service: s}
}

type productIDParam struct {
	ID int `param:"id" validate:"required"`
}

func (h *ProductHandler) RegisterRoutes(e *echo.Echo, spec *docs.OpenApi) {
	api := e.Group("/api")
	admin := e.Group("/api", middleware.RequireRoles(string(models.UserRoleAdmin)))

	r := api.GET("/products", h.List)
	echoAdapter.AddRoute[struct {
		Page  int `query:"page"`
		Limit int `query:"limit"`
	}, models.PaginatedResponse[models.Product]](spec, r, docs.OperationObject{Tags: []string{"Products API"}})

	r = api.GET("/products/:id", h.Get)
	echoAdapter.AddRoute[productIDParam, models.Product](spec, r, docs.OperationObject{Tags: []string{"Products API"}})

	r = admin.POST("/products", h.Create)
	echoAdapter.AddRoute[models.Product, models.Product](spec, r, docs.OperationObject{Tags: []string{"Products API"}})
}

func (h *ProductHandler) Create(c echo.Context) error {
	var p models.Product
	if err := c.Bind(&p); err != nil {
		log.Error().Err(err).Msg("failed to bind create product request")
		return echo.NewHTTPError(
			http.StatusBadRequest,
			&utils.ValidationError{
				Message: "Invalid request",
				Metadata: map[string]any{
					"error": err.Error(),
				},
			})
	}

	if err := utils.ValidateStruct(&p); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err)
	}

	res, err := h.service.CreateProduct(&p)
	if err != nil {
		log.Error().Err(err).Msg("failed to create product")
		return echo.NewHTTPError(
			http.StatusBadRequest,
			&utils.ValidationError{
				Message: err.Error(),
			},
		)
	}
	return c.JSON(http.StatusCreated, res)
}

func (h *ProductHandler) Get(c echo.Context) error {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		return echo.NewHTTPError(
			http.StatusBadRequest,
			&utils.ValidationError{
				Message: "Invalid product ID",
				Metadata: map[string]any{
					"field": "id", "value": idStr,
				},
			},
		)
	}
	p, err := h.service.GetProductByID(id)
	if err != nil {
		log.Error().Err(err).Int("product_id", id).Msg("failed to get product")
		return echo.NewHTTPError(
			http.StatusNotFound,
			&utils.ValidationError{
				Message: err.Error(),
			},
		)
	}
	return c.JSON(http.StatusOK, p)
}

func (h *ProductHandler) List(c echo.Context) error {
	page, _ := strconv.Atoi(c.QueryParam("page"))
	limit, _ := strconv.Atoi(c.QueryParam("limit"))
	if limit <= 0 {
		limit = 10
	}
	if page <= 0 {
		page = 1
	}

	products, total, err := h.service.ListProducts(page, limit)
	if err != nil {
		log.Error().Err(err).Msg("failed to list products")
		return echo.NewHTTPError(
			http.StatusInternalServerError,
			&utils.ValidationError{
				Message: "Failed to list products",
				Metadata: map[string]any{
					"error": err.Error(),
				},
			},
		)
	}
	totalPages := int(math.Ceil(float64(total) / float64(limit)))
	resp := &models.PaginatedResponse[models.Product]{
		Data:       products,
		Total:      total,
		Page:       page,
		Limit:      limit,
		TotalPages: totalPages,
	}
	return c.JSON(http.StatusOK, resp)
}

```

`coupon-be/internal/product/route.go`:

```go
package product

import (
	"coupon/pkg/repository"
	"coupon/pkg/service"

	"github.com/TickLabVN/tonic/core/docs"
	"github.com/labstack/echo/v4"
)

func SetupRoutes(e *echo.Echo, spec *docs.OpenApi, repo repository.ProductRepository) {
	svc := service.NewProductService(repo)
	handler := NewProductHandler(svc)
	handler.RegisterRoutes(e, spec)
}

```

`coupon-be/internal/user/handler.go`:

```go
package user

import (
	"net/http"

	"coupon/pkg/db/models"
	"coupon/pkg/service"
	"coupon/pkg/utils"

	echoAdapter "github.com/TickLabVN/tonic/adapters/echo"
	"github.com/TickLabVN/tonic/core/docs"
	"github.com/labstack/echo/v4"
)

type UserHandler struct {
	service service.UserService
}

func NewUserHandler(s service.UserService) *UserHandler {
	return &UserHandler{service: s}
}

func (h *UserHandler) RegisterRoutes(e *echo.Echo, spec *docs.OpenApi) {
	api := e.Group("/api")

	r := api.GET("/users/me", h.GetMe)
	echoAdapter.AddRoute[struct{}, models.User](spec, r, docs.OperationObject{Tags: []string{"Users API"}})
}

func (h *UserHandler) GetMe(c echo.Context) error {
	idVal := c.Get("userID")
	id, ok := idVal.(int)
	if !ok || id == 0 {
		return echo.ErrUnauthorized
	}

	user, err := h.service.GetUserByID(id)
	if err != nil {
		return echo.NewHTTPError(http.StatusNotFound, &utils.ValidationError{
			Message: "User not found",
			Metadata: map[string]any{
				"user_id": id,
			},
		})
	}

	return c.JSON(http.StatusOK, user)
}

```

`coupon-be/internal/user/route.go`:

```go
package user

import (
	"coupon/pkg/repository"
	"coupon/pkg/service"

	"github.com/TickLabVN/tonic/core/docs"
	"github.com/labstack/echo/v4"
)

func SetupRoutes(e *echo.Echo, spec *docs.OpenApi, repo repository.UserRepository) {
	svc := service.NewUserService(repo)
	handler := NewUserHandler(svc)
	handler.RegisterRoutes(e, spec)
}

```

`coupon-be/migrations/check_coupon_eligibility.sql`:

```sql
create or replace function check_coupon_eligibility (
  p_coupon_code text,
  p_user_id bigint,
  p_order_amount double precision,
  p_order_timestamp timestamp with time zone default now(),
  p_cart_items jsonb default '[]'::jsonb -- Array of {product_id, category_id, quantity, price}
) RETURNS table (
  eligible boolean,
  message text,
  coupon_id bigint,
  discount_amount double precision
) as $$
DECLARE
  v_coupon record;
  v_user record;
  v_usage_count integer;
  v_current_hour integer;
  v_current_day_of_week integer;
  v_current_date date;
  v_user_account_age_days integer;
  v_user_order_count integer;
  v_calculated_discount double precision;
  v_time_restriction_exists boolean := false;
  v_time_restriction_valid boolean := false;
  v_product_restriction_exists boolean := false;
  v_eligible_order_amount double precision := 0;
  v_cart_item jsonb;
  v_product_allowed boolean;
  v_recurrence_valid boolean;
  v_specific_user_allowed boolean := false;
  v_local_timestamp timestamp with time zone;
  rec coupon_time_restrictions%ROWTYPE;
  v_discount_type_code text;
BEGIN
  -- Get coupon details with discount type
  SELECT c.*, dt.type_code AS discount_type_code
    INTO v_coupon
    FROM coupons c
    JOIN discount_types dt ON dt.id = c.discount_type_id
   WHERE c.coupon_code = p_coupon_code
     AND c.status = 'ACTIVE';
  IF NOT FOUND THEN
    RETURN QUERY SELECT false, 'Coupon not found or inactive', NULL::bigint, 0::double precision;
    RETURN;
  END IF;

  -- Validity period
  IF p_order_timestamp < v_coupon.valid_from OR p_order_timestamp > v_coupon.valid_until THEN
    RETURN QUERY SELECT false, 'Coupon is expired or not yet valid', NULL::bigint, 0::double precision;
    RETURN;
  END IF;

  -- Get user
  SELECT * INTO v_user FROM users WHERE id = p_user_id;
  IF NOT FOUND THEN
    RETURN QUERY SELECT false, 'User not found', NULL::bigint, 0::double precision;
    RETURN;
  END IF;

  -- Global usage limit
  IF v_coupon.max_usage_count IS NOT NULL
     AND v_coupon.current_usage_count >= v_coupon.max_usage_count THEN
    RETURN QUERY SELECT false, 'Coupon usage limit exceeded globally', NULL::bigint, 0::double precision;
    RETURN;
  END IF;

  -- Per-user usage limit
  SELECT COUNT(*) INTO v_usage_count
    FROM orders o
   WHERE o.user_id = p_user_id
     AND o.applied_coupon_id = v_coupon.id;
  IF v_coupon.max_usage_per_user IS NOT NULL
     AND v_usage_count >= v_coupon.max_usage_per_user THEN
    RETURN QUERY SELECT false, 'User has exceeded usage limit for this coupon', NULL::bigint, 0::double precision;
    RETURN;
  END IF;

  -- Account age & order count
  v_user_account_age_days :=
    (EXTRACT(EPOCH FROM (p_order_timestamp - v_user.created_at)) / 86400)::integer;
  SELECT COUNT(*) INTO v_user_order_count
    FROM orders o
   WHERE o.user_id = p_user_id;

  -- User eligibility type
  -- User eligibility rules
  IF EXISTS (SELECT 1 FROM coupon_user_eligibility cue WHERE cue.coupon_id = v_coupon.id) THEN
    -- Specific user restrictions
    IF EXISTS (SELECT 1 FROM coupon_user_eligibility cue WHERE cue.coupon_id = v_coupon.id AND cue.user_id IS NOT NULL) THEN
      SELECT EXISTS(
        SELECT 1 FROM coupon_user_eligibility cue
         WHERE cue.coupon_id = v_coupon.id
           AND ((cue.user_id IS NOT NULL AND cue.user_id = p_user_id) OR (cue.user_type IS NOT NULL AND cue.user_type = v_user.type))
      ) INTO v_specific_user_allowed;
      IF NOT v_specific_user_allowed THEN
        RETURN QUERY SELECT false, 'Coupon is not available for this user', NULL::bigint, 0::double precision;
        RETURN;
      END IF;
    END IF;

    -- VIP user restriction
    IF EXISTS (SELECT 1 FROM coupon_user_eligibility cue WHERE cue.coupon_id = v_coupon.id AND cue.user_type = 'VIP') THEN
      IF v_user.type != 'VIP' THEN
        RETURN QUERY SELECT false, 'Coupon is only for VIP users', NULL::bigint, 0::double precision;
        RETURN;
      END IF;
    END IF;

    -- New or existing user based on previous orders
    IF EXISTS (
      SELECT 1 FROM coupon_user_eligibility cue
       WHERE cue.coupon_id = v_coupon.id
         AND (cue.min_previous_orders = 0 OR cue.max_previous_orders = 0)
         AND cue.user_id IS NULL
    ) THEN      
    IF v_user_order_count > 0 THEN
        RETURN QUERY SELECT false, 'Coupon is only for new users', NULL::bigint, 0::double precision;
        RETURN;
      END IF;
    ELSIF EXISTS (
      SELECT 1 FROM coupon_user_eligibility cue
       WHERE cue.coupon_id = v_coupon.id
         AND cue.min_previous_orders > 0
         AND cue.user_id IS NULL
    ) THEN
      IF v_user_order_count = 0 THEN
        RETURN QUERY SELECT false, 'Coupon is only for existing users', NULL::bigint, 0::double precision;
        RETURN;
      END IF;
    ELSIF EXISTS (
      SELECT 1 FROM coupon_user_eligibility cue
       WHERE cue.coupon_id = v_coupon.id
         AND (cue.min_account_age_days IS NOT NULL OR cue.max_account_age_days IS NOT NULL)
         AND cue.min_previous_orders IS NULL
         AND cue.max_previous_orders IS NULL
         AND cue.user_id IS NULL
    ) THEN
      IF EXISTS (
        SELECT 1 FROM coupon_user_eligibility cue
         WHERE cue.coupon_id = v_coupon.id
           AND cue.max_account_age_days IS NOT NULL
           AND cue.max_account_age_days <= 30
      ) THEN
        IF v_user_account_age_days > 30 THEN
          RETURN QUERY SELECT false, 'Coupon is only for new users', NULL::bigint, 0::double precision;
          RETURN;
        END IF;
      ELSE
        IF v_user_account_age_days <= 30 THEN
          RETURN QUERY SELECT false, 'Coupon is only for existing users', NULL::bigint, 0::double precision;
          RETURN;
        END IF;
      END IF;
    END IF;
  END IF;

  -- Time restrictions
  SELECT EXISTS(
    SELECT 1 FROM coupon_time_restrictions ctr WHERE ctr.coupon_id = v_coupon.id
  ) INTO v_time_restriction_exists;
  IF v_time_restriction_exists THEN
    v_local_timestamp := p_order_timestamp;
    v_current_hour := EXTRACT(hour FROM v_local_timestamp);
    v_current_day_of_week := EXTRACT(dow FROM v_local_timestamp);
    v_current_date := v_local_timestamp::date;
    FOR rec IN
      SELECT * FROM coupon_time_restrictions WHERE coupon_id = v_coupon.id
    LOOP
      v_time_restriction_valid := false;
      CASE rec.restriction_type
        WHEN 'DAYS_OF_WEEK' THEN
          IF rec.allowed_days_of_week IS NOT NULL
             AND v_current_day_of_week = ANY(rec.allowed_days_of_week) THEN
            v_time_restriction_valid := true;
          END IF;
        WHEN 'HOURS_OF_DAY' THEN
          IF rec.allowed_hours_start IS NOT NULL
             AND rec.allowed_hours_end IS NOT NULL THEN
            IF rec.allowed_hours_start <= rec.allowed_hours_end
               AND v_current_hour BETWEEN rec.allowed_hours_start AND rec.allowed_hours_end THEN
              v_time_restriction_valid := true;
            ELSIF rec.allowed_hours_start > rec.allowed_hours_end
                  AND (v_current_hour >= rec.allowed_hours_start OR v_current_hour <= rec.allowed_hours_end) THEN
              v_time_restriction_valid := true;
            END IF;
          END IF;
        WHEN 'SPECIFIC_DATES' THEN
          IF rec.specific_dates IS NOT NULL
             AND v_current_date = ANY(rec.specific_dates) THEN
            v_time_restriction_valid := true;
          END IF;
        WHEN 'RECURRING_DATES' THEN
          v_recurrence_valid := false;
          CASE rec.recurrence_pattern
            WHEN 'DAILY' THEN v_recurrence_valid := true;
            WHEN 'WEEKLY' THEN
              IF rec.recurrence_day_of_week IS NOT NULL
                 AND v_current_day_of_week = rec.recurrence_day_of_week THEN
                v_recurrence_valid := true;
              END IF;
            WHEN 'MONTHLY' THEN
              IF rec.recurrence_day_of_month IS NOT NULL
                 AND EXTRACT(day FROM v_local_timestamp) = rec.recurrence_day_of_month THEN
                v_recurrence_valid := true;
              END IF;
            WHEN 'YEARLY' THEN
              IF rec.recurrence_month IS NOT NULL
                 AND rec.recurrence_day_of_month IS NOT NULL
                 AND EXTRACT(month FROM v_local_timestamp) = rec.recurrence_month
                 AND EXTRACT(day FROM v_local_timestamp) = rec.recurrence_day_of_month THEN
                v_recurrence_valid := true;
              END IF;
            WHEN 'QUARTERLY' THEN
              IF rec.recurrence_day_of_month IS NOT NULL
                 AND EXTRACT(day FROM v_local_timestamp) = rec.recurrence_day_of_month
                 AND ((EXTRACT(month FROM v_local_timestamp)::int % 3) = (rec.recurrence_month % 3)) THEN
                v_recurrence_valid := true;
              END IF;
          END CASE;
          IF v_recurrence_valid THEN v_time_restriction_valid := true; END IF;
        ELSE
          v_time_restriction_valid := true;
      END CASE;
      IF NOT v_time_restriction_valid THEN
        RETURN QUERY SELECT false, 'Coupon is not valid at this time', NULL::bigint, 0::double precision;
        RETURN;
      END IF;
    END LOOP;
  END IF;

  -- Product/category restrictions
  SELECT EXISTS(
    SELECT 1 FROM coupon_product_restrictions cpr WHERE cpr.coupon_id = v_coupon.id
  ) INTO v_product_restriction_exists;
  IF v_product_restriction_exists THEN
    v_eligible_order_amount := 0;
    FOR i IN 0..jsonb_array_length(p_cart_items) - 1 LOOP
      v_cart_item := p_cart_items->i;
      v_product_allowed := false;
      SELECT EXISTS(
        SELECT 1 FROM coupon_product_restrictions cpr
         WHERE cpr.coupon_id = v_coupon.id
           AND cpr.is_included = true
           AND ((cpr.product_id IS NOT NULL AND cpr.product_id = (v_cart_item->>'product_id')::bigint)
             OR (cpr.category_id IS NOT NULL AND cpr.category_id = (v_cart_item->>'category_id')::bigint))
      ) INTO v_product_allowed;
      IF NOT v_product_allowed THEN
        SELECT NOT EXISTS(
          SELECT 1 FROM coupon_product_restrictions cpr
           WHERE cpr.coupon_id = v_coupon.id
             AND cpr.is_included = false
             AND ((cpr.product_id IS NOT NULL AND cpr.product_id = (v_cart_item->>'product_id')::bigint)
               OR (cpr.category_id IS NOT NULL AND cpr.category_id = (v_cart_item->>'category_id')::bigint))
        ) INTO v_product_allowed;
      END IF;
      IF NOT v_product_allowed THEN
        SELECT NOT EXISTS(
          SELECT 1 FROM coupon_product_restrictions cpr
           WHERE cpr.coupon_id = v_coupon.id
             AND cpr.is_included = true
        ) INTO v_product_allowed;
      END IF;
      IF v_product_allowed THEN
        v_eligible_order_amount := v_eligible_order_amount + (v_cart_item->>'price')::double precision;
      END IF;
    END LOOP;
    IF v_eligible_order_amount = 0 THEN
      RETURN QUERY SELECT false, 'No eligible products in cart for this coupon', NULL::bigint, 0::double precision;
      RETURN;
    END IF;
    p_order_amount := v_eligible_order_amount;
  END IF;

  -- Minimum order amount
  IF p_order_amount < v_coupon.min_order_amount THEN
    RETURN QUERY SELECT false, 'Minimum order amount not met', NULL::bigint, 0::double precision;
    RETURN;
  END IF;

  -- Discount calculation based on discount_type_code
  CASE v_coupon.discount_type_code
    WHEN 'PERCENT' THEN
      v_calculated_discount := p_order_amount * (v_coupon.discount_value / 100);
    WHEN 'FIXED' THEN
      -- FIXED: subtract a fixed amount from order
      v_calculated_discount := v_coupon.discount_value;
    WHEN 'FLAT' THEN
      -- FLAT: set final total to discount_value (discount = order_amount - discount_value)
      v_calculated_discount := GREATEST(p_order_amount - v_coupon.discount_value, 0);
    ELSE
      -- unknown type: no discount
      v_calculated_discount := 0;
  END CASE;
  -- apply max cap if exists
  IF v_coupon.max_discount_amount IS NOT NULL
     AND v_calculated_discount > v_coupon.max_discount_amount THEN
    v_calculated_discount := v_coupon.max_discount_amount;
  END IF;
  -- cannot exceed order amount
  IF v_calculated_discount > p_order_amount THEN
    v_calculated_discount := p_order_amount;
  END IF;
  -- round to two decimals
  v_calculated_discount := ROUND(v_calculated_discount::numeric, 2);

  RETURN QUERY SELECT true, 'Coupon is eligible', v_coupon.id, v_calculated_discount;
END;
$$ LANGUAGE plpgsql;

```

`coupon-be/migrations/ddl.sql`:

```sql
create table public.categories (
  id bigint generated by default as identity not null,
  created_at timestamp with time zone not null default (now() AT TIME ZONE 'utc'::text),
  name text not null,
  description text null,
  constraint categories_pkey primary key (id),
  constraint categories_name_key unique (name)
) TABLESPACE pg_default;
create table public.coupon_product_restrictions (
  id bigint generated by default as identity not null,
  created_at timestamp with time zone not null default (now() AT TIME ZONE 'utc'::text),
  coupon_id bigint not null,
  product_id bigint null,
  category_id bigint null,
  is_included boolean not null default true,
  constraint coupon_product_restrictions_pkey primary key (id),
  constraint coupon_product_restrictions_category_id_fkey foreign KEY (category_id) references categories (id) on update CASCADE on delete CASCADE,
  constraint coupon_product_restrictions_coupon_id_fkey foreign KEY (coupon_id) references coupons (id) on update CASCADE on delete CASCADE,
  constraint coupon_product_restrictions_product_id_fkey foreign KEY (product_id) references products (id) on update CASCADE on delete CASCADE,
  constraint coupon_product_restrictions_product_or_category_check check (
    (
      (
        (product_id is not null)
        and (category_id is null)
      )
      or (
        (product_id is null)
        and (category_id is not null)
      )
    )
  )
) TABLESPACE pg_default;
create table public.coupon_time_restrictions (
  id bigint generated by default as identity not null,
  created_at timestamp with time zone not null default (now() AT TIME ZONE 'utc'::text),
  coupon_id bigint not null,
  restriction_type public.time_restriction not null,
  allowed_days_of_week smallint[] null,
  allowed_hours_start smallint null,
  allowed_hours_end smallint null,
  specific_dates date[] null,
  recurrence_pattern public.recurrence_pattern null,
  recurrence_day_of_month smallint null,
  recurrence_month smallint null,
  recurrence_day_of_week smallint null,
  constraint coupon_time_restrictions_pkey primary key (id),
  constraint coupon_time_restrictions_coupon_id_fkey foreign KEY (coupon_id) references coupons (id) on update CASCADE on delete CASCADE,
  constraint coupon_time_restrictions_allowed_hours_check check (
    (
      (
        (allowed_hours_start is null)
        and (allowed_hours_end is null)
      )
      or (
        (
          (allowed_hours_start >= 0)
          and (allowed_hours_start <= 23)
        )
        and (
          (allowed_hours_end >= 0)
          and (allowed_hours_end <= 23)
        )
      )
    )
  ),
  constraint coupon_time_restrictions_recurrence_day_of_month_check check (
    (
      (recurrence_day_of_month is null)
      or (
        (recurrence_day_of_month >= 1)
        and (recurrence_day_of_month <= 31)
      )
    )
  ),
  constraint coupon_time_restrictions_recurrence_day_of_week_check check (
    (
      (recurrence_day_of_week is null)
      or (
        (recurrence_day_of_week >= 0)
        and (recurrence_day_of_week <= 6)
      )
    )
  ),
  constraint coupon_time_restrictions_recurrence_month_check check (
    (
      (recurrence_month is null)
      or (
        (recurrence_month >= 1)
        and (recurrence_month <= 12)
      )
    )
  )
) TABLESPACE pg_default;
create table public.coupon_user_eligibility (
  id bigint generated by default as identity not null,
  created_at timestamp with time zone not null default (now() AT TIME ZONE 'utc'::text),
  coupon_id bigint not null,
  user_id bigint null,
  user_type public.user_type null,
  min_account_age_days smallint null,
  max_account_age_days smallint null,
  min_previous_orders bigint null,
  max_previous_orders bigint null,
  constraint coupon_user_eligibility_pkey primary key (id),
  constraint coupon_user_eligibility_coupon_id_fkey foreign KEY (coupon_id) references coupons (id) on update CASCADE on delete CASCADE,
  constraint coupon_user_eligibility_user_id_fkey foreign KEY (user_id) references users (id) on update CASCADE on delete CASCADE,
  constraint coupon_user_eligibility_account_age_check check (
    (
      (
        (min_account_age_days is null)
        and (max_account_age_days is null)
      )
      or (
        (
          (min_account_age_days is null)
          or (min_account_age_days >= 0)
        )
        and (
          (max_account_age_days is null)
          or (max_account_age_days >= 0)
        )
        and (
          (min_account_age_days is null)
          or (max_account_age_days is null)
          or (min_account_age_days <= max_account_age_days)
        )
      )
    )
  ),
  constraint coupon_user_eligibility_order_count_check check (
    (
      (
        (min_previous_orders is null)
        and (max_previous_orders is null)
      )
      or (
        (
          (min_previous_orders is null)
          or (min_previous_orders >= 0)
        )
        and (
          (max_previous_orders is null)
          or (max_previous_orders >= 0)
        )
        and (
          (min_previous_orders is null)
          or (max_previous_orders is null)
          or (min_previous_orders <= max_previous_orders)
        )
      )
    )
  )
) TABLESPACE pg_default;
create table public.coupons (
  id bigint generated by default as identity not null,
  created_at timestamp with time zone not null default (now() AT TIME ZONE 'utc'::text),
  coupon_code text not null,
  title text not null,
  description text null,
  discount_type_id bigint not null,
  discount_value double precision not null,
  usage_method public.usage_method not null,
  valid_from timestamp with time zone not null,
  valid_until timestamp with time zone not null,
  max_usage_count bigint null,
  current_usage_count bigint not null default '0'::bigint,
  min_order_amount double precision not null default '0'::double precision,
  max_discount_amount double precision null,
  created_by bigint null,
  updated_at timestamp with time zone not null default (now() AT TIME ZONE 'utc'::text),
  max_usage_per_user bigint null default '1'::bigint,
  status public.coupon_status not null default 'ACTIVE'::coupon_status,
  constraint coupons_pkey primary key (id),
  constraint coupons_coupon_code_key unique (coupon_code),
  constraint coupons_created_by_fkey foreign KEY (created_by) references users (id) on update CASCADE on delete set null,
  constraint coupons_discount_type_id_fkey foreign KEY (discount_type_id) references discount_types (id) on update CASCADE on delete CASCADE,
  constraint coupons_max_usage_per_user_check check (
    (
      (max_usage_per_user is null)
      or (max_usage_per_user > 0)
    )
  ),
  constraint coupons_min_order_amount_check check ((min_order_amount >= (0)::double precision)),
  constraint coupons_valid_dates_check check ((valid_until > valid_from)),
  constraint coupons_current_usage_count_check check ((current_usage_count >= 0)),
  constraint coupons_discount_value_check check ((discount_value > (0)::double precision)),
  constraint coupons_max_usage_count_check check (
    (
      (max_usage_count is null)
      or (max_usage_count > 0)
    )
  )
) TABLESPACE pg_default;
create table public.discount_types (
  id bigint generated by default as identity not null,
  created_at timestamp with time zone not null default (now() AT TIME ZONE 'utc'::text),
  type_code text not null,
  type_name text not null,
  description text null,
  is_active boolean not null default true,
  updated_at timestamp with time zone not null default (now() AT TIME ZONE 'utc'::text),
  constraint discount_types_pkey primary key (id),
  constraint discount_types_type_code_key unique (type_code)
) TABLESPACE pg_default;
create table public.order_items (
  id bigint generated by default as identity not null,
  created_at timestamp with time zone not null default (now() AT TIME ZONE 'utc'::text),
  order_id bigint null,
  product_id bigint null,
  quantity bigint not null,
  constraint order_items_pkey primary key (id),
  constraint order_items_order_id_fkey foreign KEY (order_id) references orders (id) on update CASCADE on delete CASCADE,
  constraint order_items_product_id_fkey foreign KEY (product_id) references products (id) on update CASCADE on delete CASCADE,
  constraint order_items_quantity_check check ((quantity > 0))
) TABLESPACE pg_default;
create table public.orders (
  id bigint generated by default as identity not null,
  created_at timestamp with time zone not null default (now() AT TIME ZONE 'utc'::text),
  order_amount double precision not null,
  applied_coupon_id bigint null,
  calculation_status public.calculation_status not null default 'SUCCESS'::calculation_status,
  calculation_message text null,
  user_id bigint not null,
  constraint orders_pkey primary key (id),
  constraint orders_applied_coupon_id_fkey foreign KEY (applied_coupon_id) references coupons (id) on update CASCADE on delete set null,
  constraint orders_user_id_fkey foreign KEY (user_id) references users (id) on update CASCADE on delete set null
) TABLESPACE pg_default;
create table public.products (
  id bigint generated by default as identity not null,
  created_at timestamp with time zone not null default (now() AT TIME ZONE 'utc'::text),
  name text not null,
  description text null,
  price double precision not null,
  category_id bigint null,
  image_url text null,
  stock_quantity bigint not null default '0'::bigint,
  status public.product_status not null default 'ACTIVE'::product_status,
  brand text null,
  updated_at timestamp with time zone null default (now() AT TIME ZONE 'utc'::text),
  sku text null,
  constraint products_pkey primary key (id),
  constraint products_sku_key unique (sku),
  constraint products_category_id_fkey foreign KEY (category_id) references categories (id) on update CASCADE on delete RESTRICT,
  constraint products_price_check check ((price > (0)::double precision)),
  constraint products_stock_quantity_check check ((stock_quantity >= 0))
) TABLESPACE pg_default;
create table public.users (
  id bigint generated by default as identity not null,
  created_at timestamp with time zone not null default now(),
  email text not null,
  password_hash text not null,
  full_name text not null,
  role public.user_role not null default 'USER'::user_role,
  type public.user_type null default 'NEW'::user_type,
  constraint users_pkey primary key (id),
  constraint users_email_key unique (email)
) TABLESPACE pg_default;

```

`coupon-be/pkg/db/models/coupon.go`:

```go
package models

import "time"

type DiscountType struct {
	ID          int       `json:"id"`
	TypeCode    string    `json:"type_code"`
	TypeName    string    `json:"type_name"`
	Description string    `json:"description"`
	IsActive    bool      `json:"is_active"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

type UsageMethod string

const (
	UsageMethodManual    UsageMethod = "MANUAL"
	UsageMethodAutomatic UsageMethod = "AUTO"
)

type Coupon struct {
	ID                int         `json:"id"`
	CouponCode        string      `json:"coupon_code"`
	Title             string      `json:"title"`
	Description       string      `json:"description"`
	DiscountTypeID    int         `json:"discount_type_id"`
	DiscountValue     float64     `json:"discount_value"`
	UsageMethod       UsageMethod `json:"usage_method"`
	ValidFrom         time.Time   `json:"valid_from"`
	ValidUntil        time.Time   `json:"valid_until"`
	MaxUsageCount     *int        `json:"max_usage_count"`
	MaxUsagePerUser   *int        `json:"max_usage_per_user"`
	UserEligibility   string      `json:"user_eligibility_type"`
	CurrentUsageCount int         `json:"current_usage_count"`
	MinOrderAmount    float64     `json:"min_order_amount"`
	MaxDiscountAmount *float64    `json:"max_discount_amount"`
	CreatedBy         int         `json:"created_by,omitempty"`
	CreatedAt         time.Time   `json:"created_at"`
	UpdatedAt         time.Time   `json:"updated_at"`

	DiscountType *DiscountType `json:"discount_type,omitempty"`
	Status       string        `json:"status,omitempty"`

	ProductRestrictions  []*CouponProductRestriction `json:"product_restrictions,omitempty"`
	TimeRestrictions     []*CouponTimeRestriction    `json:"time_restrictions,omitempty"`
	UserEligibilityRules []*CouponUserEligibility    `json:"user_eligibility,omitempty"`
	UserUsage            []*UserCouponUsage          `json:"user_usage,omitempty"`
	TotalSavings         float64                     `json:"total_savings"`
	UniqueUsers          int                         `json:"unique_users"`
}

type UserCouponUsage struct {
	UserID     int                 `json:"user_id"`
	UsageCount int                 `json:"usage_count"`
	FullName   string              `json:"full_name"`
	Email      string              `json:"email"`
	Type       string              `json:"type"`
	Orders     []*CouponOrderUsage `json:"orders,omitempty"`
}

type CouponOrderUsage struct {
	OrderID     int       `json:"order_id"`
	UsedAt      time.Time `json:"used_at"`
	OrderAmount float64   `json:"order_amount"`
	Status      string    `json:"status"`
}

type CreateCouponRequest struct {
	CouponCode        string      `json:"coupon_code"         validate:"required,min=3,max=50"`
	Title             string      `json:"title"               validate:"required,min=1,max=255"`
	Description       string      `json:"description"`
	DiscountTypeID    int         `json:"discount_type_id"    validate:"required"`
	DiscountValue     float64     `json:"discount_value"      validate:"required,gt=0"`
	UsageMethod       UsageMethod `json:"usage_method"        validate:"required"`
	ValidFrom         time.Time   `json:"valid_from"          validate:"required"`
	ValidUntil        time.Time   `json:"valid_until"         validate:"required"`
	MaxUsageCount     *int        `json:"max_usage_count"`
	MaxUsagePerUser   *int        `json:"max_usage_per_user"`
	MinOrderAmount    float64     `json:"min_order_amount"    validate:"gte=0"`
	MaxDiscountAmount *float64    `json:"max_discount_amount"`
}

type UpdateCouponRequest struct {
	Title             string      `json:"title"               validate:"required,min=1,max=255"`
	Description       string      `json:"description"`
	DiscountTypeID    int         `json:"discount_type_id"    validate:"required"`
	DiscountValue     float64     `json:"discount_value"      validate:"required,gt=0"`
	UsageMethod       UsageMethod `json:"usage_method"        validate:"required"`
	Status            string      `json:"status"              validate:"required"`
	MinOrderAmount    float64     `json:"min_order_amount"    validate:"gte=0"`
	MaxDiscountAmount *float64    `json:"max_discount_amount"`
	MaxUsageCount     *int        `json:"max_usage_count"`
	MaxUsagePerUser   *int        `json:"max_usage_per_user"`
	ValidFrom         time.Time   `json:"valid_from"          validate:"required"`
	ValidUntil        time.Time   `json:"valid_until"         validate:"required"`

	ProductRestrictions []*CouponProductRestriction `json:"product_restrictions"`
	TimeRestrictions    []*CouponTimeRestriction    `json:"time_restrictions"`
	UserEligibility     []*CouponUserEligibility    `json:"user_eligibility"`
}

type ListCouponsRequest struct {
	Page           int          `json:"page" query:"page"`
	Limit          int          `json:"limit" query:"limit"`
	Search         string       `json:"search" query:"search"`
	DiscountTypeID *int         `json:"discount_type_id" query:"discount_type_id"`
	UsageMethod    *UsageMethod `json:"usage_method" query:"usage_method"`
	Status         string       `json:"status" query:"status"`
	SortBy         string       `json:"sort_by" query:"sort_by"`
	SortOrder      string       `json:"sort_order" query:"sort_order"`
}

type PaginatedResponse[T any] struct {
	Data       []T `json:"data"`
	Total      int `json:"total"`
	Page       int `json:"page"`
	Limit      int `json:"limit"`
	TotalPages int `json:"total_pages"`
}

type CartItem struct {
	ProductID  *int    `json:"product_id,omitempty"`
	CategoryID *int    `json:"category_id,omitempty"`
	Quantity   int     `json:"quantity"`
	Price      float64 `json:"price"`
}

type CouponEligibilityRequest struct {
	CouponCode     string     `json:"coupon_code" validate:"required"`
	UserID         int        `json:"user_id" validate:"required"`
	OrderAmount    float64    `json:"order_amount" validate:"required,gt=0"`
	OrderTimestamp time.Time  `json:"order_timestamp"`
	CartItems      []CartItem `json:"cart_items"`
}

type CouponEligibilityResponse struct {
	Eligible       bool    `json:"eligible"`
	Message        string  `json:"message"`
	CouponID       *int    `json:"coupon_id,omitempty"`
	DiscountAmount float64 `json:"discount_amount"`
}

type AutoCouponEligibilityRequest struct {
	UserID         int        `json:"user_id" validate:"required"`
	OrderAmount    float64    `json:"order_amount" validate:"required,gt=0"`
	OrderTimestamp time.Time  `json:"order_timestamp"`
	CartItems      []CartItem `json:"cart_items"`
}

type EligibleCoupon struct {
	Eligible       bool    `json:"eligible"`
	Coupon         *Coupon `json:"coupon"`
	DiscountAmount float64 `json:"discount_amount"`
}

type CouponProductRestriction struct {
	ID           int       `json:"id"`
	CreatedAt    time.Time `json:"created_at"`
	CouponID     int       `json:"coupon_id"`
	ProductID    *int      `json:"product_id,omitempty"`
	CategoryID   *int      `json:"category_id,omitempty"`
	ProductName  *string   `json:"product_name,omitempty"`
	CategoryName *string   `json:"category_name,omitempty"`
	IsIncluded   bool      `json:"is_included"`
}

type CouponTimeRestriction struct {
	ID                   int         `json:"id"`
	CreatedAt            time.Time   `json:"created_at"`
	CouponID             int         `json:"coupon_id"`
	RestrictionType      string      `json:"restriction_type"`
	AllowedDaysOfWeek    []int32     `json:"allowed_days_of_week"`
	AllowedHoursStart    *int32      `json:"allowed_hours_start,omitempty"`
	AllowedHoursEnd      *int32      `json:"allowed_hours_end,omitempty"`
	SpecificDates        []time.Time `json:"specific_dates"`
	RecurrencePattern    *string     `json:"recurrence_pattern,omitempty"`
	RecurrenceDayOfMonth *int32      `json:"recurrence_day_of_month,omitempty"`
	RecurrenceMonth      *int32      `json:"recurrence_month,omitempty"`
	RecurrenceDayOfWeek  *int32      `json:"recurrence_day_of_week,omitempty"`
}

type CouponUserEligibility struct {
	ID                int       `json:"id"`
	CreatedAt         time.Time `json:"created_at"`
	CouponID          int       `json:"coupon_id"`
	UserID            *int      `json:"user_id,omitempty"`
	UserType          *string   `json:"user_type,omitempty"`
	MinAccountAgeDays *int32    `json:"min_account_age_days,omitempty"`
	MaxAccountAgeDays *int32    `json:"max_account_age_days,omitempty"`
	MinPreviousOrders *int64    `json:"min_previous_orders,omitempty"`
	MaxPreviousOrders *int64    `json:"max_previous_orders,omitempty"`
}

```

`coupon-be/pkg/db/models/order.go`:

```go
package models

import "time"

type Order struct {
	ID                 int       `json:"id"`
	OrderAmount        float64   `json:"order_amount"`
	AppliedCouponID    *int      `json:"applied_coupon_id"`
	DiscountAmount     float64   `json:"discount_amount"`
	CalculationStatus  string    `json:"calculation_status"`
	CalculationMessage string    `json:"calculation_message"`
	UserID             int       `json:"user_id"`
	CreatedAt          time.Time `json:"created_at"`

	Items []OrderItem `json:"items,omitempty"`

	AppliedCoupon *Coupon `json:"applied_coupon,omitempty"`
}

type CreateOrderRequest struct {
	OrderAmount        float64     `json:"order_amount"`
	AppliedCouponID    *int        `json:"applied_coupon_id"`
	CalculationStatus  string      `json:"calculation_status"`
	CalculationMessage string      `json:"calculation_message"`
	UserID             int         `json:"user_id"`
	Items              []OrderItem `json:"items"`
}

type OrderCalculationRequest struct {
	OrderAmount    float64     `json:"order_amount"    validate:"required,gt=0"`
	OrderTimestamp time.Time   `json:"order_timestamp" validate:"required"`
	CouponCode     *string     `json:"coupon_code"`
	UserID         int         `json:"user_id" validate:"required"`
	Items          []OrderItem `json:"items"`
}

type OrderCalculationResponse struct {
	OrderAmount       float64 `json:"order_amount"`
	CouponCode        *string `json:"coupon_code"`
	AppliedCouponID   *int    `json:"applied_coupon_id"`
	AppliedCouponCode *string `json:"applied_coupon_code"`
	DiscountAmount    float64 `json:"discount_amount"`
	FinalAmount       float64 `json:"final_amount"`
	Status            string  `json:"status"`
	Message           string  `json:"message"`
}

```

`coupon-be/pkg/db/models/product.go`:

```go
package models

import "time"

type Category struct {
	ID          int       `json:"id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	CreatedAt   time.Time `json:"created_at"`
}

type ProductStatus string

const (
	ProductStatusActive   ProductStatus = "ACTIVE"
	ProductStatusInactive ProductStatus = "INACTIVE"
)

type Product struct {
	ID            int           `json:"id"`
	Name          string        `json:"name"`
	Description   string        `json:"description"`
	Price         float64       `json:"price"`
	CategoryID    *int          `json:"category_id"`
	ImageURL      *string       `json:"image_url"`
	StockQuantity int           `json:"stock_quantity"`
	Status        ProductStatus `json:"status"`
	Brand         *string       `json:"brand"`
	SKU           *string       `json:"sku"`
	CreatedAt     time.Time     `json:"created_at"`
	UpdatedAt     *time.Time    `json:"updated_at"`
}

type OrderItem struct {
	ID        int       `json:"id"`
	OrderID   int       `json:"order_id"`
	ProductID int       `json:"product_id"`
	Quantity  int       `json:"quantity"`
	CreatedAt time.Time `json:"created_at"`
}

```

`coupon-be/pkg/db/models/user.go`:

```go
package models

import "time"

type UserRole string

const (
	UserRoleUser  UserRole = "USER"
	UserRoleAdmin UserRole = "ADMIN"
)

type UserType string

const (
	UserTypeNew UserType = "NEW"
	UserTypeVIP UserType = "VIP"
)

type User struct {
	ID           int       `json:"id"`
	Email        string    `json:"email"`
	PasswordHash string    `json:"-"`
	FullName     string    `json:"full_name"`
	Role         UserRole  `json:"role"`
	Type         UserType  `json:"type"`
	CreatedAt    time.Time `json:"created_at"`
}

type LoginRequest struct {
	Email    string `json:"email" validate:"required,email"`
	Password string `json:"password" validate:"required"`
}

type SignupRequest struct {
	Email    string `json:"email" validate:"required,email"`
	Password string `json:"password" validate:"required"`
	FullName string `json:"full_name" validate:"required"`
}

```

`coupon-be/pkg/db/postgresql/init.go`:

```go
package postgresql

import (
	"database/sql"
	"errors"
	"fmt"
	"os"
	"sync"

	_ "github.com/lib/pq"
	"github.com/rs/zerolog/log"
)

type Database struct {
	db   *sql.DB
	once sync.Once
}

func NewDatabase() *Database {
	return &Database{}
}

func (db *Database) connectDatasource(dataSourceName string) error {
	var err error
	db.once.Do(func() {
		db.db, err = sql.Open("postgres", dataSourceName)
		if err != nil {
			return
		}
		err = db.db.Ping()
	})
	return err
}

func InitDB() *sql.DB {
	postgresUri := os.Getenv("POSTGRES_URI")
	if len(postgresUri) == 0 {
		panic("POSTGRES_URI is required")
	}

	db := NewDatabase()
	err := db.connectDatasource(postgresUri)
	if err != nil {
		panic(fmt.Sprintf("Failed to initialize database: %v", err))
	}

	log.Info().Msg("PostgreSQL connected")

	return db.db
}

func (db *Database) GetDB() (*sql.DB, error) {
	if db.db == nil {
		return nil, errors.New("Database not initialized - Call InitDB first")
	}
	return db.db, nil
}

func (db *Database) CloseDB() error {
	if db.db != nil {
		return db.db.Close()
	}
	return nil
}

```

`coupon-be/pkg/repository/coupon_repository.go`:

```go
package repository

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"coupon/pkg/db/models"

	"github.com/lib/pq"
)

type CouponRepository interface {
	Create(coupon *models.CreateCouponRequest, createdBy int) (*models.Coupon, error)
	GetByID(id int) (*models.Coupon, error)
	GetByCode(code string) (*models.Coupon, error)
	UpdateTx(tx *sql.Tx, id int, coupon *models.UpdateCouponRequest) error
	ReplaceProductRestrictionsTx(tx *sql.Tx, couponID int, restrictions []*models.CouponProductRestriction) error
	ReplaceTimeRestrictionsTx(tx *sql.Tx, couponID int, restrictions []*models.CouponTimeRestriction) error
	ReplaceUserEligibilityTx(tx *sql.Tx, couponID int, rules []*models.CouponUserEligibility) error
	List(req *models.ListCouponsRequest) ([]*models.Coupon, int, error)
	GetDiscountTypes() ([]*models.DiscountType, error)
	GetActiveCouponsForAutoApply(orderAmount float64, orderTime time.Time) ([]*models.Coupon, error)
	IncrementUsageCountTx(tx *sql.Tx, id int) error
	CheckCouponEligibility(req *models.CouponEligibilityRequest) (*models.CouponEligibilityResponse, error)
	GetEligibleAutoCoupons(req *models.AutoCouponEligibilityRequest) ([]*models.EligibleCoupon, error)
	GetUserEligibilityRules(couponID int) ([]*models.CouponUserEligibility, error)
}

type couponRepository struct {
	db *sql.DB
}

func NewCouponRepository(db *sql.DB) CouponRepository {
	return &couponRepository{db: db}
}

func (r *couponRepository) Create(req *models.CreateCouponRequest, createdBy int) (*models.Coupon, error) {
	query := `
		INSERT INTO coupons (
    	coupon_code, title, description, discount_type_id, discount_value,
      usage_method, valid_from, valid_until, max_usage_count,
      min_order_amount, max_discount_amount, created_by,
      max_usage_per_user
    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
    RETURNING id, created_at, updated_at	`
	var id int
	var createdAt, updatedAt time.Time

	err := r.db.QueryRow(query,
		req.CouponCode, req.Title, req.Description, req.DiscountTypeID,
		req.DiscountValue, req.UsageMethod, req.ValidFrom, req.ValidUntil,
		req.MaxUsageCount, req.MinOrderAmount, req.MaxDiscountAmount, createdBy,
		req.MaxUsagePerUser,
	).Scan(&id, &createdAt, &updatedAt)
	if err != nil {
		return nil, err
	}

	return r.GetByID(id)
}

func (r *couponRepository) GetByID(id int) (*models.Coupon, error) {
	query := `
		SELECT 
			c.id, c.coupon_code, c.title, c.description, c.discount_type_id,
			c.discount_value, c.usage_method, c.valid_from, c.valid_until,
			c.max_usage_count, c.max_usage_per_user,
			c.current_usage_count, c.min_order_amount,
			c.max_discount_amount, c.status, c.created_by, c.created_at, c.updated_at,
			dt.id, dt.type_code, dt.type_name, dt.description
		FROM coupons c
		JOIN discount_types dt ON c.discount_type_id = dt.id
		WHERE c.id = $1
	`

	coupon := &models.Coupon{
		DiscountType: &models.DiscountType{},
	}

	err := r.db.QueryRow(query, id).Scan(
		&coupon.ID, &coupon.CouponCode, &coupon.Title, &coupon.Description,
		&coupon.DiscountTypeID, &coupon.DiscountValue, &coupon.UsageMethod,
		&coupon.ValidFrom, &coupon.ValidUntil, &coupon.MaxUsageCount, &coupon.MaxUsagePerUser,
		&coupon.CurrentUsageCount, &coupon.MinOrderAmount, &coupon.MaxDiscountAmount,
		&coupon.Status, &coupon.CreatedBy, &coupon.CreatedAt, &coupon.UpdatedAt,
		&coupon.DiscountType.ID, &coupon.DiscountType.TypeCode, &coupon.DiscountType.TypeName,
		&coupon.DiscountType.Description,
	)
	if err != nil {
		return nil, err
	}

	coupon.Status = r.calculateStatus(coupon)

	if pr, err := r.getProductRestrictions(id); err == nil {
		coupon.ProductRestrictions = pr
	}
	if tr, err := r.getTimeRestrictions(id); err == nil {
		coupon.TimeRestrictions = tr
	}
	if ue, err := r.getUserEligibility(id); err == nil {
		coupon.UserEligibilityRules = ue
	}
	if usage, err := r.getUserCouponUsage(id); err == nil {
		coupon.UserUsage = usage
	}
	if savings, err := r.getTotalSavings(id); err == nil {
		coupon.TotalSavings = savings
	}
	if users, err := r.getUniqueUsers(id); err == nil {
		coupon.UniqueUsers = users
	}

	return coupon, nil
}

func (r *couponRepository) GetByCode(code string) (*models.Coupon, error) {
	query := `
		SELECT 
			c.id, c.coupon_code, c.title, c.description, c.discount_type_id,
			c.discount_value, c.usage_method, c.valid_from, c.valid_until,
			c.max_usage_count, c.max_usage_per_user, c.current_usage_count, c.min_order_amount,
			c.max_discount_amount, c.status, c.created_by, c.created_at, c.updated_at,
			dt.id, dt.type_code, dt.type_name, dt.description
		FROM coupons c
		JOIN discount_types dt ON c.discount_type_id = dt.id
		WHERE c.coupon_code = $1
	`

	coupon := &models.Coupon{
		DiscountType: &models.DiscountType{},
	}

	err := r.db.QueryRow(query, code).Scan(
		&coupon.ID, &coupon.CouponCode, &coupon.Title, &coupon.Description,
		&coupon.DiscountTypeID, &coupon.DiscountValue, &coupon.UsageMethod,
		&coupon.ValidFrom, &coupon.ValidUntil, &coupon.MaxUsageCount, &coupon.MaxUsagePerUser,
		&coupon.CurrentUsageCount, &coupon.MinOrderAmount, &coupon.MaxDiscountAmount,
		&coupon.Status, &coupon.CreatedBy, &coupon.CreatedAt, &coupon.UpdatedAt,
		&coupon.DiscountType.ID, &coupon.DiscountType.TypeCode, &coupon.DiscountType.TypeName,
		&coupon.DiscountType.Description,
	)
	if err != nil {
		return nil, err
	}

	coupon.Status = r.calculateStatus(coupon)
	return coupon, nil
}

func (r *couponRepository) UpdateTx(tx *sql.Tx, id int, req *models.UpdateCouponRequest) error {
	query := `
		UPDATE coupons SET
			title = $1, description = $2, discount_type_id = $3, discount_value = $4,
			usage_method = $5, valid_from = $6, valid_until = $7, max_usage_count = $8,
			min_order_amount = $9, max_discount_amount = $10, status = $11,
			max_usage_per_user = $12, updated_at = CURRENT_TIMESTAMP
		WHERE id = $13
	`

	_, err := tx.Exec(query,
		req.Title, req.Description, req.DiscountTypeID, req.DiscountValue,
		req.UsageMethod, req.ValidFrom, req.ValidUntil, req.MaxUsageCount,
		req.MinOrderAmount, req.MaxDiscountAmount, req.Status,
		req.MaxUsagePerUser, id,
	)
	return err
}

func (r *couponRepository) ReplaceProductRestrictionsTx(tx *sql.Tx, couponID int, restrictions []*models.CouponProductRestriction) error {
	if _, err := tx.Exec(`DELETE FROM coupon_product_restrictions WHERE coupon_id = $1`, couponID); err != nil {
		return err
	}
	if len(restrictions) == 0 {
		return nil
	}
	const query = `INSERT INTO coupon_product_restrictions (coupon_id, product_id, category_id, is_included) VALUES ($1, $2, $3, $4)`
	for _, rstr := range restrictions {
		if _, err := tx.Exec(query, couponID, rstr.ProductID, rstr.CategoryID, rstr.IsIncluded); err != nil {
			return err
		}
	}
	return nil
}

func (r *couponRepository) ReplaceTimeRestrictionsTx(tx *sql.Tx, couponID int, restrictions []*models.CouponTimeRestriction) error {
	if _, err := tx.Exec(`DELETE FROM coupon_time_restrictions WHERE coupon_id = $1`, couponID); err != nil {
		return err
	}
	if len(restrictions) == 0 {
		return nil
	}
	const query = `INSERT INTO coupon_time_restrictions (
                coupon_id, restriction_type, allowed_days_of_week, allowed_hours_start, allowed_hours_end,
                specific_dates, recurrence_pattern, recurrence_day_of_month, recurrence_month, recurrence_day_of_week
       ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)`
	for _, rstr := range restrictions {
		days := []int64{}
		for _, d := range rstr.AllowedDaysOfWeek {
			days = append(days, int64(d))
		}
		dates := []string{}
		for _, d := range rstr.SpecificDates {
			dates = append(dates, d.Format("2006-01-02"))
		}
		if _, err := tx.Exec(query, couponID, rstr.RestrictionType, pq.Array(days), rstr.AllowedHoursStart,
			rstr.AllowedHoursEnd, pq.Array(dates), rstr.RecurrencePattern,
			rstr.RecurrenceDayOfMonth, rstr.RecurrenceMonth, rstr.RecurrenceDayOfWeek); err != nil {
			return err
		}
	}
	return nil
}

func (r *couponRepository) ReplaceUserEligibilityTx(tx *sql.Tx, couponID int, rules []*models.CouponUserEligibility) error {
	if _, err := tx.Exec(`DELETE FROM coupon_user_eligibility WHERE coupon_id = $1`, couponID); err != nil {
		return err
	}
	if len(rules) == 0 {
		return nil
	}
	const query = `INSERT INTO coupon_user_eligibility (
                coupon_id, user_id, user_type, min_account_age_days, max_account_age_days,
                min_previous_orders, max_previous_orders
       ) VALUES ($1, $2, $3, $4, $5, $6, $7)`
	for _, rstr := range rules {
		if _, err := tx.Exec(query, couponID, rstr.UserID, rstr.UserType, rstr.MinAccountAgeDays,
			rstr.MaxAccountAgeDays, rstr.MinPreviousOrders, rstr.MaxPreviousOrders); err != nil {
			return err
		}
	}
	return nil
}

func (r *couponRepository) List(req *models.ListCouponsRequest) ([]*models.Coupon, int, error) {
	whereConditions := []string{}
	args := []any{}
	paramCount := 0

	if req.Search != "" {
		paramCount++
		searchParam1 := fmt.Sprintf("$%d", paramCount)
		paramCount++
		searchParam2 := fmt.Sprintf("$%d", paramCount)
		whereConditions = append(whereConditions, fmt.Sprintf("(c.coupon_code LIKE %s OR c.title LIKE %s)", searchParam1, searchParam2))
		searchTerm := "%" + req.Search + "%"
		args = append(args, searchTerm, searchTerm)
	}

	if req.DiscountTypeID != nil {
		paramCount++
		whereConditions = append(whereConditions, fmt.Sprintf("c.discount_type_id = $%d", paramCount))
		args = append(args, *req.DiscountTypeID)
	}

	if req.UsageMethod != nil {
		paramCount++
		whereConditions = append(whereConditions, fmt.Sprintf("c.usage_method = $%d", paramCount))
		args = append(args, *req.UsageMethod)
	}

	whereClause := ""
	if len(whereConditions) > 0 {
		whereClause = "WHERE " + strings.Join(whereConditions, " AND ")
	}

	orderBy := "ORDER BY c.created_at DESC"
	if req.SortBy != "" {
		direction := "ASC"
		if req.SortOrder == "desc" {
			direction = "DESC"
		}
		orderBy = fmt.Sprintf("ORDER BY c.%s %s", req.SortBy, direction)
	}

	offset := (req.Page - 1) * req.Limit

	countQuery := fmt.Sprintf(`
		SELECT COUNT(*) 
		FROM coupons c 
		JOIN discount_types dt ON c.discount_type_id = dt.id
		%s
	`, whereClause)

	var total int
	err := r.db.QueryRow(countQuery, args...).Scan(&total)
	if err != nil {
		return nil, 0, err
	}

	paramCount++
	limitParam := fmt.Sprintf("$%d", paramCount)
	paramCount++
	offsetParam := fmt.Sprintf("$%d", paramCount)

	query := fmt.Sprintf(`
		SELECT 
			c.id, c.coupon_code, c.title, c.description, c.discount_type_id,
			c.discount_value, c.usage_method, c.valid_from, c.valid_until,
			c.max_usage_count, c.max_usage_per_user,
			c.current_usage_count, c.min_order_amount,
			c.max_discount_amount, c.status, c.created_by, c.created_at, c.updated_at,
			dt.id, dt.type_code, dt.type_name, dt.description
		FROM coupons c
		JOIN discount_types dt ON c.discount_type_id = dt.id
		%s
		%s
		LIMIT %s OFFSET %s
	`, whereClause, orderBy, limitParam, offsetParam)

	args = append(args, req.Limit, offset)

	rows, err := r.db.Query(query, args...)
	if err != nil {
		return nil, 0, err
	}
	defer rows.Close()

	var coupons []*models.Coupon
	for rows.Next() {
		coupon := &models.Coupon{
			DiscountType: &models.DiscountType{},
		}

		err := rows.Scan(
			&coupon.ID, &coupon.CouponCode, &coupon.Title, &coupon.Description,
			&coupon.DiscountTypeID, &coupon.DiscountValue, &coupon.UsageMethod,
			&coupon.ValidFrom, &coupon.ValidUntil, &coupon.MaxUsageCount,
			&coupon.MaxUsagePerUser,
			&coupon.CurrentUsageCount, &coupon.MinOrderAmount, &coupon.MaxDiscountAmount,
			&coupon.Status, &coupon.CreatedBy, &coupon.CreatedAt, &coupon.UpdatedAt,
			&coupon.DiscountType.ID, &coupon.DiscountType.TypeCode, &coupon.DiscountType.TypeName,
			&coupon.DiscountType.Description,
		)
		if err != nil {
			return nil, 0, err
		}

		coupon.Status = r.calculateStatus(coupon)
		coupons = append(coupons, coupon)
	}

	return coupons, total, nil
}

func (r *couponRepository) GetDiscountTypes() ([]*models.DiscountType, error) {
	query := `SELECT id, type_code, type_name, description, is_active, created_at, updated_at FROM discount_types WHERE is_active = TRUE ORDER BY type_name`

	rows, err := r.db.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var types []*models.DiscountType
	for rows.Next() {
		dt := &models.DiscountType{}
		err := rows.Scan(&dt.ID, &dt.TypeCode, &dt.TypeName, &dt.Description, &dt.IsActive, &dt.CreatedAt, &dt.UpdatedAt)
		if err != nil {
			return nil, err
		}
		types = append(types, dt)
	}

	return types, nil
}

func (r *couponRepository) GetActiveCouponsForAutoApply(orderAmount float64, orderTime time.Time) ([]*models.Coupon, error) {
	query := `
		SELECT 
			c.id, c.coupon_code, c.title, c.description, c.discount_type_id,
			c.discount_value, c.usage_method, c.valid_from, c.valid_until,
			c.max_usage_count, c.current_usage_count, c.min_order_amount,
			c.max_discount_amount, c.status, c.created_by, c.created_at, c.updated_at,
			dt.id, dt.type_code, dt.type_name, dt.description
		FROM coupons c
		JOIN discount_types dt ON c.discount_type_id = dt.id
		WHERE c.status = 'ACTIVE'
		  AND c.usage_method = 'AUTO'
		  AND $1 BETWEEN c.valid_from AND c.valid_until
		  AND $2 >= c.min_order_amount
		  AND (c.max_usage_count IS NULL OR c.current_usage_count < c.max_usage_count)
		ORDER BY c.discount_value DESC
	`

	rows, err := r.db.Query(query, orderTime, orderAmount)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var coupons []*models.Coupon
	for rows.Next() {
		coupon := &models.Coupon{
			DiscountType: &models.DiscountType{},
		}

		err := rows.Scan(
			&coupon.ID, &coupon.CouponCode, &coupon.Title, &coupon.Description,
			&coupon.DiscountTypeID, &coupon.DiscountValue, &coupon.UsageMethod,
			&coupon.ValidFrom, &coupon.ValidUntil, &coupon.MaxUsageCount,
			&coupon.CurrentUsageCount, &coupon.MinOrderAmount, &coupon.MaxDiscountAmount,
			&coupon.Status, &coupon.CreatedBy, &coupon.CreatedAt, &coupon.UpdatedAt,
			&coupon.DiscountType.ID, &coupon.DiscountType.TypeCode, &coupon.DiscountType.TypeName,
			&coupon.DiscountType.Description,
		)
		if err != nil {
			return nil, err
		}

		coupons = append(coupons, coupon)
	}

	return coupons, nil
}

func (r *couponRepository) IncrementUsageCountTx(tx *sql.Tx, id int) error {
	query := `UPDATE coupons SET current_usage_count = current_usage_count + 1 WHERE id = $1`
	_, err := tx.Exec(query, id)
	return err
}

func (r *couponRepository) CheckCouponEligibility(req *models.CouponEligibilityRequest) (*models.CouponEligibilityResponse, error) {
	query := `SELECT eligible, message, coupon_id, discount_amount FROM check_coupon_eligibility($1, $2, $3, $4, $5)`

	cartItems, err := json.Marshal(req.CartItems)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal cart items: %w", err)
	}

	var (
		eligible       bool
		message        string
		couponID       sql.NullInt64
		discountAmount float64
	)

	err = r.db.QueryRow(query, req.CouponCode, req.UserID, req.OrderAmount, req.OrderTimestamp, cartItems).
		Scan(&eligible, &message, &couponID, &discountAmount)
	if err != nil {
		return nil, fmt.Errorf("failed to execute eligibility check: %w", err)
	}

	resp := &models.CouponEligibilityResponse{
		Eligible:       eligible,
		Message:        message,
		DiscountAmount: discountAmount,
	}
	if couponID.Valid {
		id := int(couponID.Int64)
		resp.CouponID = &id
	}

	return resp, nil
}

func (r *couponRepository) GetEligibleAutoCoupons(req *models.AutoCouponEligibilityRequest) ([]*models.EligibleCoupon, error) {
	query := `
  	SELECT
    	c.id, c.coupon_code, c.title, c.description, c.discount_type_id,
    	c.discount_value, c.usage_method, c.valid_from, c.valid_until,
    	c.max_usage_count, c.max_usage_per_user,
    	c.current_usage_count, c.min_order_amount, c.max_discount_amount,
    	c.status, c.created_by, c.created_at, c.updated_at,
    	dt.id, dt.type_code, dt.type_name, dt.description,
    	eligibility_check.discount_amount
		FROM coupons c
		JOIN discount_types dt ON c.discount_type_id = dt.id
		JOIN LATERAL check_coupon_eligibility(
       c.coupon_code,
       $1,
       $2,
       $3,
       $4
    ) AS eligibility_check ON TRUE
    WHERE c.usage_method = 'AUTO'  
			AND c.status = 'ACTIVE'
  		AND eligibility_check.eligible = true
		ORDER BY eligibility_check.discount_amount DESC
	`

	cartItems, err := json.Marshal(req.CartItems)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal cart items: %w", err)
	}

	rows, err := r.db.Query(query, req.UserID, req.OrderAmount, req.OrderTimestamp, cartItems)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var results []*models.EligibleCoupon
	for rows.Next() {
		coupon := &models.Coupon{DiscountType: &models.DiscountType{}}
		var discountAmount float64
		if err := rows.Scan(
			&coupon.ID, &coupon.CouponCode, &coupon.Title, &coupon.Description,
			&coupon.DiscountTypeID, &coupon.DiscountValue, &coupon.UsageMethod,
			&coupon.ValidFrom, &coupon.ValidUntil, &coupon.MaxUsageCount,
			&coupon.MaxUsagePerUser,
			&coupon.CurrentUsageCount, &coupon.MinOrderAmount, &coupon.MaxDiscountAmount,
			&coupon.Status, &coupon.CreatedBy, &coupon.CreatedAt, &coupon.UpdatedAt,
			&coupon.DiscountType.ID, &coupon.DiscountType.TypeCode, &coupon.DiscountType.TypeName,
			&coupon.DiscountType.Description, &discountAmount,
		); err != nil {
			return nil, err
		}
		coupon.Status = r.calculateStatus(coupon)
		results = append(results, &models.EligibleCoupon{Eligible: true, Coupon: coupon, DiscountAmount: discountAmount})
	}

	return results, nil
}

func (r *couponRepository) GetUserEligibilityRules(couponID int) ([]*models.CouponUserEligibility, error) {
	return r.getUserEligibility(couponID)
}

func (r *couponRepository) getProductRestrictions(couponID int) ([]*models.CouponProductRestriction, error) {
	rows, err := r.db.Query(`
		SELECT cpr.id, cpr.created_at, cpr.coupon_id, cpr.product_id, cpr.category_id, cpr.is_included,
          p.name AS product_name, c.name AS category_name
    FROM coupon_product_restrictions cpr
    LEFT JOIN products p ON cpr.product_id = p.id
    LEFT JOIN categories c ON cpr.category_id = c.id
    WHERE cpr.coupon_id = $1
	`, couponID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var res []*models.CouponProductRestriction
	for rows.Next() {
		cpr := &models.CouponProductRestriction{}
		if err := rows.Scan(
			&cpr.ID,
			&cpr.CreatedAt,
			&cpr.CouponID,
			&cpr.ProductID,
			&cpr.CategoryID,
			&cpr.IsIncluded,
			&cpr.ProductName,
			&cpr.CategoryName,
		); err != nil {
			return nil, err
		}
		res = append(res, cpr)
	}
	return res, nil
}

func (r *couponRepository) getTimeRestrictions(couponID int) ([]*models.CouponTimeRestriction, error) {
	rows, err := r.db.Query(`
		SELECT id, created_at, coupon_id, restriction_type, allowed_days_of_week, allowed_hours_start, allowed_hours_end,
					specific_dates, recurrence_pattern, recurrence_day_of_month, recurrence_month, recurrence_day_of_week
		FROM coupon_time_restrictions
		WHERE coupon_id = $1
	`, couponID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var res []*models.CouponTimeRestriction
	for rows.Next() {
		ctr := &models.CouponTimeRestriction{}
		var days []int64
		var dateStrings []string

		if err := rows.Scan(
			&ctr.ID, &ctr.CreatedAt, &ctr.CouponID, &ctr.RestrictionType, pq.Array(&days), &ctr.AllowedHoursStart, &ctr.AllowedHoursEnd,
			pq.Array(&dateStrings), &ctr.RecurrencePattern, &ctr.RecurrenceDayOfMonth, &ctr.RecurrenceMonth, &ctr.RecurrenceDayOfWeek); err != nil {
			return nil, err
		}
		for _, d := range days {
			ctr.AllowedDaysOfWeek = append(ctr.AllowedDaysOfWeek, int32(d))
		}
		for _, s := range dateStrings {
			if t, err := time.Parse("2006-01-02", s); err == nil {
				ctr.SpecificDates = append(ctr.SpecificDates, t)
			}
		}
		res = append(res, ctr)
	}
	return res, nil
}

func (r *couponRepository) getUserEligibility(couponID int) ([]*models.CouponUserEligibility, error) {
	rows, err := r.db.Query(`
		SELECT id, created_at, coupon_id, user_id, user_type, min_account_age_days, max_account_age_days, min_previous_orders, max_previous_orders
		FROM coupon_user_eligibility 
		WHERE coupon_id = $1
	`, couponID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var res []*models.CouponUserEligibility
	for rows.Next() {
		cue := &models.CouponUserEligibility{}
		if err := rows.Scan(
			&cue.ID, &cue.CreatedAt, &cue.CouponID, &cue.UserID, &cue.UserType, &cue.MinAccountAgeDays, &cue.MaxAccountAgeDays,
			&cue.MinPreviousOrders, &cue.MaxPreviousOrders); err != nil {
			return nil, err
		}
		res = append(res, cue)
	}
	return res, nil
}

func (r *couponRepository) getUserCouponUsage(couponID int) ([]*models.UserCouponUsage, error) {
	rows, err := r.db.Query(`
		SELECT u.id, u.full_name, u.email, u.type,
        	o.id, o.created_at, o.order_amount, o.calculation_status
  	FROM orders o
  	JOIN users u ON o.user_id = u.id
  	WHERE o.applied_coupon_id = $1
  	ORDER BY o.created_at DESC
  `, couponID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	usageMap := map[int]*models.UserCouponUsage{}
	for rows.Next() {
		var (
			userID          int
			fullName, email string
			userType        string
			orderID         int
			createdAt       time.Time
			orderAmount     float64
			status          string
		)

		if err := rows.Scan(&userID, &fullName, &email, &userType, &orderID, &createdAt, &orderAmount, &status); err != nil {
			return nil, err
		}

		u, ok := usageMap[userID]
		if !ok {
			u = &models.UserCouponUsage{
				UserID:   userID,
				FullName: fullName,
				Email:    email,
				Type:     userType,
			}
			usageMap[userID] = u
		}

		u.UsageCount++
		u.Orders = append(u.Orders, &models.CouponOrderUsage{
			OrderID:     orderID,
			UsedAt:      createdAt,
			OrderAmount: orderAmount,
			Status:      status,
		})
	}

	var res []*models.UserCouponUsage
	for _, u := range usageMap {
		res = append(res, u)
	}
	return res, nil
}

func (r *couponRepository) getTotalSavings(couponID int) (float64, error) {
	var savings sql.NullFloat64
	query := `
              SELECT COALESCE(SUM(ROUND((CASE dt.type_code
                       WHEN 'PERCENT' THEN LEAST(o.order_amount * c.discount_value / 100,
                                               COALESCE(c.max_discount_amount, o.order_amount))
                       WHEN 'FIXED' THEN LEAST(c.discount_value,
                                               COALESCE(c.max_discount_amount, o.order_amount))
                       WHEN 'FLAT' THEN LEAST(GREATEST(o.order_amount - c.discount_value, 0),
                                               COALESCE(c.max_discount_amount, o.order_amount))
                       ELSE 0 END)::numeric, 2)), 0)
               FROM orders o
               JOIN coupons c ON o.applied_coupon_id = c.id
               JOIN discount_types dt ON c.discount_type_id = dt.id
               WHERE o.applied_coupon_id = $1`
	err := r.db.QueryRow(query, couponID).Scan(&savings)
	if err != nil {
		return 0, err
	}
	if savings.Valid {
		return savings.Float64, nil
	}
	return 0, nil
}

func (r *couponRepository) getUniqueUsers(couponID int) (int, error) {
	var count sql.NullInt64
	err := r.db.QueryRow(`SELECT COUNT(DISTINCT user_id) FROM orders WHERE applied_coupon_id = $1`, couponID).Scan(&count)
	if err != nil {
		return 0, err
	}
	if count.Valid {
		return int(count.Int64), nil
	}
	return 0, nil
}

func (r *couponRepository) calculateStatus(coupon *models.Coupon) string {
	now := time.Now()

	if strings.ToUpper(coupon.Status) != "ACTIVE" {
		return coupon.Status
	}

	if now.After(coupon.ValidUntil) {
		return "EXPIRED"
	}

	if coupon.MaxUsageCount != nil && coupon.CurrentUsageCount >= *coupon.MaxUsageCount {
		return "USED_UP"
	}

	if now.Before(coupon.ValidFrom) {
		return "PENDING"
	}

	return "ACTIVE"
}

```

`coupon-be/pkg/repository/order_repository.go`:

```go
package repository

import (
	"database/sql"
	"fmt"

	"coupon/pkg/db/models"
)

type OrderRepository interface {
	CreateTx(tx *sql.Tx, req *models.CreateOrderRequest) (*models.Order, error)
	AddItemsTx(tx *sql.Tx, orderID int, items []models.OrderItem) error
	GetByID(id int) (*models.Order, error)
	List(page, limit int, search string) ([]models.Order, int, error)
	GetByCouponID(couponID int, page, limit int) ([]models.Order, int, error)
}

type orderRepository struct {
	db *sql.DB
}

func NewOrderRepository(db *sql.DB) OrderRepository {
	return &orderRepository{db: db}
}

func (r *orderRepository) CreateTx(tx *sql.Tx, req *models.CreateOrderRequest) (*models.Order, error) {
	const query = `
		INSERT INTO orders (
    	order_amount, applied_coupon_id,
      calculation_status, calculation_message,
      user_id
    )
    VALUES ($1, $2, $3, $4, $5)
    RETURNING id, order_amount, applied_coupon_id,
    					calculation_status, calculation_message,
              user_id, created_at`

	order := &models.Order{}
	err := tx.QueryRow(query,
		req.OrderAmount, req.AppliedCouponID,
		req.CalculationStatus, req.CalculationMessage,
		req.UserID,
	).Scan(
		&order.ID,
		&order.OrderAmount,
		&order.AppliedCouponID,
		&order.CalculationStatus,
		&order.CalculationMessage,
		&order.UserID,
		&order.CreatedAt,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create order: %w", err)
	}

	return order, nil
}

func (r *orderRepository) GetByID(id int) (*models.Order, error) {
	const query = `
		SELECT o.id, o.order_amount, o.applied_coupon_id,
    			o.calculation_status, o.calculation_message,
        	o.user_id, o.created_at,
          c.id, c.coupon_code, c.title, c.description, c.discount_type_id, c.discount_value,
        	c.usage_method, c.valid_from, c.valid_until, c.max_usage_count, c.current_usage_count,
        	c.min_order_amount, c.max_discount_amount, c.status, c.created_by, c.created_at, c.updated_at
    FROM orders o
    LEFT JOIN coupons c ON o.applied_coupon_id = c.id
    WHERE o.id = $1`
	order := &models.Order{}
	var coupon models.Coupon
	var hasCoupon bool

	err := r.db.QueryRow(query, id).Scan(
		&order.ID,
		&order.OrderAmount,
		&order.AppliedCouponID,
		&order.CalculationStatus,
		&order.CalculationMessage,
		&order.UserID,
		&order.CreatedAt,
		&coupon.ID,
		&coupon.CouponCode,
		&coupon.Title,
		&coupon.Description,
		&coupon.DiscountTypeID,
		&coupon.DiscountValue,
		&coupon.UsageMethod,
		&coupon.ValidFrom,
		&coupon.ValidUntil,
		&coupon.MaxUsageCount,
		&coupon.CurrentUsageCount,
		&coupon.MinOrderAmount,
		&coupon.MaxDiscountAmount,
		&coupon.Status,
		&coupon.CreatedBy,
		&coupon.CreatedAt,
		&coupon.UpdatedAt,
	)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("order not found")
		}
		return nil, fmt.Errorf("failed to get order: %w", err)
	}

	if coupon.ID != 0 {
		hasCoupon = true
		order.AppliedCoupon = &coupon
	}

	if !hasCoupon {
		order.AppliedCoupon = nil
	}

	return order, nil
}

func (r *orderRepository) List(page, limit int, search string) ([]models.Order, int, error) {
	offset := (page - 1) * limit

	countQuery := `
		SELECT COUNT(*) FROM orders o
		WHERE ($1 = '' OR o.calculation_status::text ILIKE '%' || $1 || '%')`

	var total int
	err := r.db.QueryRow(countQuery, search).Scan(&total)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count orders: %w", err)
	}

	dataQuery := `
	SELECT o.id, o.order_amount, o.applied_coupon_id,
        o.calculation_status, o.calculation_message,
      	o.user_id, o.created_at,
        c.id, c.coupon_code, c.title, c.description, c.discount_type_id, c.discount_value,
        c.usage_method, c.valid_from, c.valid_until, c.max_usage_count, c.current_usage_count,
        c.min_order_amount, c.max_discount_amount, c.status, c.created_by, c.created_at, c.updated_at
  FROM orders o
  LEFT JOIN coupons c ON o.applied_coupon_id = c.id
  WHERE ($1 = '' OR o.calculation_status::text ILIKE '%' || $1 || '%')
  ORDER BY o.created_at DESC
  LIMIT $2 OFFSET $3`
	rows, err := r.db.Query(dataQuery, search, limit, offset)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to list orders: %w", err)
	}
	defer rows.Close()

	var orders []models.Order
	for rows.Next() {
		var order models.Order
		var coupon models.Coupon
		var couponID sql.NullInt64

		err := rows.Scan(
			&order.ID,
			&order.OrderAmount,
			&order.AppliedCouponID,
			&order.CalculationStatus,
			&order.CalculationMessage,
			&order.UserID,
			&order.CreatedAt,
			&couponID,
			&coupon.CouponCode,
			&coupon.Title,
			&coupon.Description,
			&coupon.DiscountTypeID,
			&coupon.DiscountValue,
			&coupon.UsageMethod,
			&coupon.ValidFrom,
			&coupon.ValidUntil,
			&coupon.MaxUsageCount,
			&coupon.CurrentUsageCount,
			&coupon.MinOrderAmount,
			&coupon.MaxDiscountAmount,
			&coupon.Status,
			&coupon.CreatedBy,
			&coupon.CreatedAt,
			&coupon.UpdatedAt,
		)
		if err != nil {
			return nil, 0, fmt.Errorf("failed to scan order: %w", err)
		}

		if couponID.Valid {
			coupon.ID = int(couponID.Int64)
			order.AppliedCoupon = &coupon
		}

		orders = append(orders, order)
	}

	return orders, total, nil
}

func (r *orderRepository) GetByCouponID(couponID int, page, limit int) ([]models.Order, int, error) {
	offset := (page - 1) * limit

	countQuery := `SELECT COUNT(*) FROM orders WHERE applied_coupon_id = $1`
	var total int
	err := r.db.QueryRow(countQuery, couponID).Scan(&total)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count orders by coupon: %w", err)
	}

	dataQuery := `
		SELECT o.id, o.order_amount, o.applied_coupon_id,
          o.calculation_status, o.calculation_message,
          o.user_id, o.created_at,
					c.id, c.coupon_code, c.title, c.description, c.discount_type_id, c.discount_value,
          c.usage_method, c.valid_from, c.valid_until, c.max_usage_count, c.current_usage_count,
          c.min_order_amount, c.max_discount_amount, c.status, c.created_by, c.created_at, c.updated_at
    FROM orders o
  	LEFT JOIN coupons c ON o.applied_coupon_id = c.id
    WHERE o.applied_coupon_id = $1
    ORDER BY o.created_at DESC
    LIMIT $2 OFFSET $3`
	rows, err := r.db.Query(dataQuery, couponID, limit, offset)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to list orders by coupon: %w", err)
	}
	defer rows.Close()

	var orders []models.Order
	for rows.Next() {
		var order models.Order
		var coupon models.Coupon
		var hasCoupon bool

		err := rows.Scan(
			&order.ID,
			&order.OrderAmount,
			&order.AppliedCouponID,
			&order.CalculationStatus,
			&order.CalculationMessage,
			&order.UserID,
			&order.CreatedAt,
			&coupon.ID,
			&coupon.CouponCode,
			&coupon.Title,
			&coupon.Description,
			&coupon.DiscountTypeID,
			&coupon.DiscountValue,
			&coupon.UsageMethod,
			&coupon.ValidFrom,
			&coupon.ValidUntil,
			&coupon.MaxUsageCount,
			&coupon.CurrentUsageCount,
			&coupon.MinOrderAmount,
			&coupon.MaxDiscountAmount,
			&coupon.Status,
			&coupon.CreatedBy,
			&coupon.CreatedAt,
			&coupon.UpdatedAt,
		)
		if err != nil {
			return nil, 0, fmt.Errorf("failed to scan order: %w", err)
		}
		if coupon.ID != 0 {
			hasCoupon = true
			order.AppliedCoupon = &coupon
		}
		if !hasCoupon {
			order.AppliedCoupon = nil
		}

		orders = append(orders, order)
	}

	return orders, total, nil
}

func (r *orderRepository) AddItemsTx(tx *sql.Tx, orderID int, items []models.OrderItem) error {
	if len(items) == 0 {
		return nil
	}
	const query = `INSERT INTO order_items (order_id, product_id, quantity) VALUES ($1, $2, $3)`
	for _, it := range items {
		if _, err := tx.Exec(query, orderID, it.ProductID, it.Quantity); err != nil {
			return fmt.Errorf("failed to insert order item: %w", err)
		}
	}
	return nil
}

```

`coupon-be/pkg/repository/product_repository.go`:

```go
package repository

import (
	"database/sql"
	"fmt"

	"coupon/pkg/db/models"
)

type ProductRepository interface {
	Create(p *models.Product) (*models.Product, error)
	GetByID(id int) (*models.Product, error)
	List(offset, limit int) ([]models.Product, int, error)
}

type productRepository struct {
	db *sql.DB
}

func NewProductRepository(db *sql.DB) ProductRepository {
	return &productRepository{db: db}
}

func (r *productRepository) Create(p *models.Product) (*models.Product, error) {
	const query = `INSERT INTO products (name, description, price, category_id, image_url, stock_quantity, status, brand, sku)
        VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9)
        RETURNING id, created_at, updated_at`
	err := r.db.QueryRow(query, p.Name, p.Description, p.Price, p.CategoryID, p.ImageURL, p.StockQuantity, p.Status, p.Brand, p.SKU).
		Scan(&p.ID, &p.CreatedAt, &p.UpdatedAt)
	if err != nil {
		return nil, fmt.Errorf("failed to create product: %w", err)
	}
	return p, nil
}

func (r *productRepository) GetByID(id int) (*models.Product, error) {
	const query = `SELECT id, name, description, price, category_id, image_url, stock_quantity, status, brand, updated_at, created_at, sku FROM products WHERE id=$1`
	p := &models.Product{}
	err := r.db.QueryRow(query, id).Scan(&p.ID, &p.Name, &p.Description, &p.Price, &p.CategoryID, &p.ImageURL, &p.StockQuantity, &p.Status, &p.Brand, &p.UpdatedAt, &p.CreatedAt, &p.SKU)
	if err != nil {
		return nil, fmt.Errorf("failed to get product: %w", err)
	}
	return p, nil
}

func (r *productRepository) List(offset, limit int) ([]models.Product, int, error) {
	const countQuery = `SELECT COUNT(*) FROM products`
	var total int
	if err := r.db.QueryRow(countQuery).Scan(&total); err != nil {
		return nil, 0, fmt.Errorf("failed to count products: %w", err)
	}
	const query = `SELECT id, name, description, price, category_id, image_url, stock_quantity, status, brand, updated_at, created_at, sku
        FROM products ORDER BY created_at DESC LIMIT $1 OFFSET $2`
	rows, err := r.db.Query(query, limit, offset)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to list products: %w", err)
	}
	defer rows.Close()
	var res []models.Product
	for rows.Next() {
		var p models.Product
		err := rows.Scan(&p.ID, &p.Name, &p.Description, &p.Price, &p.CategoryID, &p.ImageURL, &p.StockQuantity, &p.Status, &p.Brand, &p.UpdatedAt, &p.CreatedAt, &p.SKU)
		if err != nil {
			return nil, 0, fmt.Errorf("failed to scan product: %w", err)
		}
		res = append(res, p)
	}
	if err := rows.Err(); err != nil {
		return nil, 0, fmt.Errorf("failed to list products: %w", err)
	}
	return res, total, nil
}

```

`coupon-be/pkg/repository/user_repository.go`:

```go
package repository

import (
	"database/sql"

	"coupon/pkg/db/models"
)

type UserRepository interface {
	GetByEmail(email string) (*models.User, error)
	GetByID(id int) (*models.User, error)
	Create(email, passwordHash, fullName string) (*models.User, error)
}

type userRepository struct {
	db *sql.DB
}

func NewUserRepository(db *sql.DB) UserRepository {
	return &userRepository{db: db}
}

func (r *userRepository) GetByEmail(email string) (*models.User, error) {
	const query = `SELECT id, email, password_hash, full_name, role, type, created_at FROM users WHERE email = $1`
	user := &models.User{}
	var userType sql.NullString
	err := r.db.QueryRow(query, email).Scan(&user.ID, &user.Email, &user.PasswordHash, &user.FullName, &user.Role, &userType, &user.CreatedAt)
	if err != nil {
		return nil, err
	}
	if userType.Valid {
		user.Type = models.UserType(userType.String)
	}
	return user, nil
}

func (r *userRepository) GetByID(id int) (*models.User, error) {
	const query = `SELECT id, email, password_hash, full_name, role, type, created_at FROM users WHERE id = $1`
	user := &models.User{}
	var userType sql.NullString
	err := r.db.QueryRow(query, id).Scan(&user.ID, &user.Email, &user.PasswordHash, &user.FullName, &user.Role, &userType, &user.CreatedAt)
	if err != nil {
		return nil, err
	}
	if userType.Valid {
		user.Type = models.UserType(userType.String)
	}
	return user, nil
}

func (r *userRepository) Create(email, passwordHash, fullName string) (*models.User, error) {
	const query = `INSERT INTO users (email, password_hash, full_name) VALUES ($1, $2, $3)
                RETURNING id, email, password_hash, full_name, role, type, created_at`

	user := &models.User{}
	var userType sql.NullString
	err := r.db.QueryRow(query, email, passwordHash, fullName).Scan(
		&user.ID, &user.Email, &user.PasswordHash, &user.FullName, &user.Role, &userType, &user.CreatedAt,
	)
	if err != nil {
		return nil, err
	}
	if userType.Valid {
		user.Type = models.UserType(userType.String)
	}

	return user, nil
}

```

`coupon-be/pkg/service/auth_service.go`:

```go
package service

import (
	"database/sql"
	"fmt"

	"coupon/pkg/db/models"
	"coupon/pkg/repository"

	"golang.org/x/crypto/bcrypt"
)

type AuthService interface {
	Login(req *models.LoginRequest) (*models.User, error)
	Signup(req *models.SignupRequest) (*models.User, error)
}

type authService struct {
	repo repository.UserRepository
}

func NewAuthService(repo repository.UserRepository) AuthService {
	return &authService{repo: repo}
}

func (s *authService) Login(req *models.LoginRequest) (*models.User, error) {
	user, err := s.repo.GetByEmail(req.Email)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("user not found")
		}
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	if err := bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(req.Password)); err != nil {
		return nil, fmt.Errorf("invalid credentials")
	}

	return user, nil
}

func (s *authService) Signup(req *models.SignupRequest) (*models.User, error) {
	_, err := s.repo.GetByEmail(req.Email)
	if err != nil && err != sql.ErrNoRows {
		return nil, fmt.Errorf("failed to check existing user: %w", err)
	}
	if err == nil {
		return nil, fmt.Errorf("email already exists")
	}

	hash, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		return nil, fmt.Errorf("failed to hash password: %w", err)
	}

	user, err := s.repo.Create(req.Email, string(hash), req.FullName)
	if err != nil {
		return nil, fmt.Errorf("failed to create user: %w", err)
	}

	return user, nil
}

```

`coupon-be/pkg/service/coupon_service.go`:

```go
package service

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"math"
	"time"

	"coupon/pkg/db/models"
	"coupon/pkg/repository"
	"coupon/pkg/utils"
)

type CouponService interface {
	CreateCoupon(req *models.CreateCouponRequest, userID int) (*models.Coupon, error)
	GetCouponByID(id int) (*models.Coupon, error)
	GetCouponByCode(code string) (*models.Coupon, error)
	UpdateCoupon(id int, req *models.UpdateCouponRequest) (*models.Coupon, error)
	ListCoupons(req *models.ListCouponsRequest) (*models.PaginatedResponse[*models.Coupon], error)
	GetDiscountTypes() ([]*models.DiscountType, error)
	CheckCouponEligibility(req *models.CouponEligibilityRequest) (*models.CouponEligibilityResponse, error)
	ListEligibleAutoCoupons(req *models.AutoCouponEligibilityRequest) ([]*models.EligibleCoupon, error)
}

type couponService struct {
	db   *sql.DB
	repo repository.CouponRepository
}

func NewCouponService(db *sql.DB, repo repository.CouponRepository) CouponService {
	return &couponService{db: db, repo: repo}
}

func (s *couponService) CreateCoupon(req *models.CreateCouponRequest, userID int) (*models.Coupon, error) {
	if err := s.validateCouponRequest(req); err != nil {
		return nil, err
	}

	existing, err := s.repo.GetByCode(req.CouponCode)
	if err != nil && err != sql.ErrNoRows {
		return nil, fmt.Errorf("failed to check existing coupon: %w", err)
	}
	if existing != nil {
		return nil, &utils.ValidationError{
			Message: "Coupon code already exists",
			Metadata: map[string]any{
				"field": "coupon_code",
				"value": req.CouponCode,
			},
		}
	}

	if err := s.validateDiscountTypeAndUsageMethod(req.DiscountTypeID, req.UsageMethod); err != nil {
		return nil, err
	}

	coupon, err := s.repo.Create(req, userID)
	if err != nil {
		return nil, err
	}

	if rules, err := s.repo.GetUserEligibilityRules(coupon.ID); err == nil {
		coupon.UserEligibilityRules = rules
		coupon.UserEligibility = determineEligibilityType(rules)
	}

	return coupon, nil
}

func (s *couponService) GetCouponByID(id int) (*models.Coupon, error) {
	coupon, err := s.repo.GetByID(id)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("coupon not found")
		}
		return nil, fmt.Errorf("failed to get coupon: %w", err)
	}
	if rules, err := s.repo.GetUserEligibilityRules(coupon.ID); err == nil {
		coupon.UserEligibilityRules = rules
		coupon.UserEligibility = determineEligibilityType(rules)
	}
	return coupon, nil
}

func (s *couponService) GetCouponByCode(code string) (*models.Coupon, error) {
	coupon, err := s.repo.GetByCode(code)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("coupon not found")
		}
		return nil, fmt.Errorf("failed to get coupon: %w", err)
	}
	if rules, err := s.repo.GetUserEligibilityRules(coupon.ID); err == nil {
		coupon.UserEligibilityRules = rules
		coupon.UserEligibility = determineEligibilityType(rules)
	}
	return coupon, nil
}

func (s *couponService) UpdateCoupon(id int, req *models.UpdateCouponRequest) (*models.Coupon, error) {
	existing, err := s.repo.GetByID(id)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("coupon not found")
		}
		return nil, fmt.Errorf("failed to get coupon: %w", err)
	}

	if existing.CurrentUsageCount > 0 {
		return nil, &utils.ValidationError{
			Message:  "Coupon has already been used and cannot be updated",
			Metadata: map[string]any{"coupon_id": id},
		}
	}

	if err := s.validateCouponRequest(req); err != nil {
		return nil, err
	}

	if err := s.validateDiscountTypeAndUsageMethod(req.DiscountTypeID, req.UsageMethod); err != nil {
		return nil, err
	}

	tx, err := s.db.BeginTx(context.Background(), nil)
	if err != nil {
		return nil, fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()

	if err := s.repo.UpdateTx(tx, id, req); err != nil {
		return nil, err
	}
	if err := s.repo.ReplaceProductRestrictionsTx(tx, id, req.ProductRestrictions); err != nil {
		return nil, err
	}
	if err := s.repo.ReplaceTimeRestrictionsTx(tx, id, req.TimeRestrictions); err != nil {
		return nil, err
	}
	if err := s.repo.ReplaceUserEligibilityTx(tx, id, req.UserEligibility); err != nil {
		return nil, err
	}

	if err := tx.Commit(); err != nil {
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}

	coupon, err := s.repo.GetByID(id)
	if err != nil {
		return nil, err
	}

	if rules, err := s.repo.GetUserEligibilityRules(coupon.ID); err == nil {
		coupon.UserEligibilityRules = rules
		coupon.UserEligibility = determineEligibilityType(rules)
	}

	return coupon, nil
}

func (s *couponService) ListCoupons(req *models.ListCouponsRequest) (*models.PaginatedResponse[*models.Coupon], error) {
	coupons, total, err := s.repo.List(req)
	if err != nil {
		return nil, fmt.Errorf("failed to list coupons: %w", err)
	}

	for _, c := range coupons {
		if rules, err := s.repo.GetUserEligibilityRules(c.ID); err == nil {
			c.UserEligibilityRules = rules
			c.UserEligibility = determineEligibilityType(rules)
		}
	}

	totalPages := int(math.Ceil(float64(total) / float64(req.Limit)))

	return &models.PaginatedResponse[*models.Coupon]{
		Data:       coupons,
		Total:      total,
		Page:       req.Page,
		Limit:      req.Limit,
		TotalPages: totalPages,
	}, nil
}

func (s *couponService) GetDiscountTypes() ([]*models.DiscountType, error) {
	return s.repo.GetDiscountTypes()
}

func (s *couponService) CheckCouponEligibility(req *models.CouponEligibilityRequest) (*models.CouponEligibilityResponse, error) {
	if err := utils.ValidateStruct(req); err != nil {
		return nil, err
	}

	if req.OrderTimestamp.IsZero() {
		req.OrderTimestamp = time.Now()
	}
	resp, err := s.repo.CheckCouponEligibility(req)
	if err != nil {
		return nil, fmt.Errorf("failed to check coupon eligibility: %w", err)
	}

	return resp, nil
}

func (s *couponService) ListEligibleAutoCoupons(req *models.AutoCouponEligibilityRequest) ([]*models.EligibleCoupon, error) {
	if err := utils.ValidateStruct(req); err != nil {
		return nil, err
	}
	if req.OrderTimestamp.IsZero() {
		req.OrderTimestamp = time.Now()
	}
	coupons, err := s.repo.GetEligibleAutoCoupons(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get eligible coupons: %w", err)
	}
	for _, c := range coupons {
		if c.Coupon != nil {
			if rules, err := s.repo.GetUserEligibilityRules(c.Coupon.ID); err == nil {
				c.Coupon.UserEligibilityRules = rules
				c.Coupon.UserEligibility = determineEligibilityType(rules)
			}
		}
	}
	return coupons, nil
}

func (s *couponService) validateCouponRequest(req any) error {
	switch v := req.(type) {
	case *models.CreateCouponRequest:
		if err := s.validateFields(v.DiscountValue, v.MaxUsageCount, v.MinOrderAmount, v.MaxDiscountAmount, v.ValidFrom, v.ValidUntil); err != nil {
			return err
		}
		return s.validateExtraFields(v.MaxUsagePerUser)
	case *models.UpdateCouponRequest:
		if err := s.validateFields(v.DiscountValue, v.MaxUsageCount, v.MinOrderAmount, v.MaxDiscountAmount, v.ValidFrom, v.ValidUntil); err != nil {
			return err
		}
		return s.validateExtraFields(v.MaxUsagePerUser)
	default:
		return errors.New("invalid coupon request type")
	}
}

func (s *couponService) validateExtraFields(maxUsagePerUser *int) error {
	if maxUsagePerUser != nil && *maxUsagePerUser <= 0 {
		return &utils.ValidationError{
			Message:  "Max usage per user must be greater than 0",
			Metadata: map[string]any{"field": "max_usage_per_user", "value": *maxUsagePerUser},
		}
	}

	return nil
}

func (s *couponService) validateFields(discountValue float64, maxUsageCount *int, minOrderAmount float64, maxDiscountAmount *float64, validFrom, validUntil time.Time) error {
	if validFrom.After(validUntil) {
		return &utils.ValidationError{
			Message: "Valid from date must be before valid until date",
			Metadata: map[string]any{
				"valid_from":  validFrom,
				"valid_until": validUntil,
			},
		}
	}

	if validUntil.Before(time.Now()) {
		return &utils.ValidationError{
			Message: "Valid until date cannot be in the past",
			Metadata: map[string]any{
				"valid_until": validUntil,
			},
		}
	}

	if discountValue <= 0 {
		return &utils.ValidationError{
			Message: "Discount value must be greater than 0",
			Metadata: map[string]any{
				"field": "discount_value",
				"value": discountValue,
			},
		}
	}

	if maxUsageCount != nil && *maxUsageCount <= 0 {
		return &utils.ValidationError{
			Message: "Max usage count must be greater than 0",
			Metadata: map[string]any{
				"field": "max_usage_count",
				"value": *maxUsageCount,
			},
		}
	}

	if minOrderAmount < 0 {
		return &utils.ValidationError{
			Message: "Minimum order amount cannot be negative",
			Metadata: map[string]any{
				"field": "min_order_amount",
				"value": minOrderAmount,
			},
		}
	}

	if maxDiscountAmount != nil && *maxDiscountAmount <= 0 {
		return &utils.ValidationError{
			Message: "Max discount amount must be greater than 0",
			Metadata: map[string]any{
				"field": "max_discount_amount",
				"value": *maxDiscountAmount,
			},
		}
	}

	if maxDiscountAmount != nil && discountValue > *maxDiscountAmount {
		return &utils.ValidationError{
			Message: "Discount value cannot exceed max discount amount",
			Metadata: map[string]any{
				"field":          "max_discount_amount",
				"max_value":      *maxDiscountAmount,
				"discount_value": discountValue,
			},
		}
	}
	return nil
}

func determineEligibilityType(rules []*models.CouponUserEligibility) string {
	if len(rules) == 0 {
		return "ALL"
	}

	for _, r := range rules {
		if r.UserID != nil {
			return "SPECIFIC_USERS"
		}
	}

	for _, r := range rules {
		if r.UserType != nil && *r.UserType == "VIP" {
			return "VIP_USERS"
		}
	}

	for _, r := range rules {
		if r.UserID == nil {
			if (r.MinPreviousOrders != nil && *r.MinPreviousOrders == 0) || (r.MaxPreviousOrders != nil && *r.MaxPreviousOrders == 0) {
				return "NEW_USERS"
			}
		}
	}

	for _, r := range rules {
		if r.UserID == nil && r.MinPreviousOrders != nil && *r.MinPreviousOrders > 0 {
			return "ALL"
		}
	}

	hasAge := false
	newUsers := false
	for _, r := range rules {
		if r.UserID == nil && (r.MinAccountAgeDays != nil || r.MaxAccountAgeDays != nil) && r.MinPreviousOrders == nil && r.MaxPreviousOrders == nil {
			hasAge = true
			if r.MaxAccountAgeDays != nil && *r.MaxAccountAgeDays <= 30 {
				newUsers = true
			}
		}
	}
	if hasAge {
		if newUsers {
			return "NEW_USERS"
		}
		return "ALL"
	}

	return "ALL"
}

func (s *couponService) validateDiscountTypeAndUsageMethod(discountTypeID int, usageMethod models.UsageMethod) error {
	discountTypes, err := s.repo.GetDiscountTypes()
	if err != nil {
		return fmt.Errorf("failed to validate discount type: %w", err)
	}

	discountTypeValid := false
	for _, dt := range discountTypes {
		if dt.ID == discountTypeID {
			discountTypeValid = true
			break
		}
	}

	if !discountTypeValid {
		return &utils.ValidationError{
			Message: "Invalid discount type",
			Metadata: map[string]any{
				"field": "discount_type_id",
				"value": discountTypeID,
			},
		}
	}

	if usageMethod != models.UsageMethodManual && usageMethod != models.UsageMethodAutomatic {
		return &utils.ValidationError{
			Message: "Invalid usage method",
			Metadata: map[string]any{
				"field": "usage_method",
				"value": usageMethod,
			},
		}
	}

	return nil
}

```

`coupon-be/pkg/service/order_service.go`:

```go
package service

import (
	"context"
	"database/sql"
	"fmt"
	"math"
	"strings"
	"time"

	"coupon/pkg/db/models"
	"coupon/pkg/repository"

	"github.com/rs/zerolog/log"
)

type OrderService interface {
	CalculateDiscount(req *models.OrderCalculationRequest) (*models.OrderCalculationResponse, error)
	CreateOrder(req *models.OrderCalculationRequest) (*models.Order, error)
	GetOrderByID(id int) (*models.Order, error)
	ListOrders(page, limit int, search string) ([]models.Order, int, error)
	ListOrdersByCoupon(couponID int, page, limit int) ([]models.Order, int, error)
}

type orderService struct {
	db         *sql.DB
	orderRepo  repository.OrderRepository
	couponRepo repository.CouponRepository
}

func NewOrderService(db *sql.DB, orderRepo repository.OrderRepository, couponRepo repository.CouponRepository) OrderService {
	return &orderService{
		db:         db,
		orderRepo:  orderRepo,
		couponRepo: couponRepo,
	}
}

func (s *orderService) CalculateDiscount(req *models.OrderCalculationRequest) (*models.OrderCalculationResponse, error) {
	resp := &models.OrderCalculationResponse{
		OrderAmount:    req.OrderAmount,
		CouponCode:     req.CouponCode,
		DiscountAmount: 0,
		FinalAmount:    req.OrderAmount,
		Status:         "SUCCESS",
		Message:        "No coupon applied",
	}

	var selectedCoupon *models.Coupon
	var err error

	if req.CouponCode != nil && *req.CouponCode != "" {
		selectedCoupon, err = s.handleManualCouponApplication(*req.CouponCode, req.OrderAmount, req.OrderTimestamp)
		if err != nil {
			resp.Status = "FAILED"
			resp.Message = err.Error()
			return resp, nil
		}
	} else {
		selectedCoupon, err = s.handleAutomaticCouponApplication(req.OrderAmount, req.OrderTimestamp)
		if err != nil {
			log.Warn().Err(err).Msg("Failed to apply automatic coupon")
		}
	}

	if selectedCoupon != nil {
		discountAmount, err := s.calculateDiscount(selectedCoupon, req.OrderAmount)
		if err != nil {
			resp.Status = "FAILED"
			resp.Message = fmt.Sprintf("Failed to calculate discount: %v", err)
			return resp, nil
		}

		resp.AppliedCouponID = &selectedCoupon.ID
		resp.AppliedCouponCode = &selectedCoupon.CouponCode
		resp.DiscountAmount = discountAmount
		resp.FinalAmount = req.OrderAmount - discountAmount
		resp.Message = fmt.Sprintf("Coupon '%s' applied successfully", selectedCoupon.CouponCode)

		if resp.FinalAmount < 0 {
			resp.FinalAmount = 0
		}
	}

	return resp, nil
}

func (s *orderService) CreateOrder(req *models.OrderCalculationRequest) (*models.Order, error) {
	calculation, err := s.CalculateDiscount(req)
	if err != nil {
		return nil, fmt.Errorf("failed to calculate discount: %w", err)
	}

	orderReq := &models.CreateOrderRequest{
		OrderAmount:        req.OrderAmount,
		AppliedCouponID:    calculation.AppliedCouponID,
		CalculationStatus:  calculation.Status,
		CalculationMessage: calculation.Message,
		UserID:             req.UserID,
		Items:              req.Items,
	}

	tx, err := s.db.BeginTx(context.Background(), nil)
	if err != nil {
		return nil, fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()

	order, err := s.orderRepo.CreateTx(tx, orderReq)
	if err != nil {
		return nil, fmt.Errorf("failed to create order: %w", err)
	}

	if err := s.orderRepo.AddItemsTx(tx, order.ID, orderReq.Items); err != nil {
		return nil, fmt.Errorf("failed to create order items: %w", err)
	}

	if calculation.AppliedCouponID != nil && calculation.Status == "SUCCESS" {
		if err := s.couponRepo.IncrementUsageCountTx(tx, *calculation.AppliedCouponID); err != nil {
			return nil, fmt.Errorf("failed to increment usage count: %w", err)
		}
	}

	if err := tx.Commit(); err != nil {
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}

	return order, nil
}

func (s *orderService) GetOrderByID(id int) (*models.Order, error) {
	order, err := s.orderRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get order: %w", err)
	}
	s.populateOrderDiscount(order)
	return order, nil
}

func (s *orderService) ListOrders(page, limit int, search string) ([]models.Order, int, error) {
	if page <= 0 {
		page = 1
	}
	if limit <= 0 || limit > 100 {
		limit = 10
	}

	orders, total, err := s.orderRepo.List(page, limit, search)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to list orders: %w", err)
	}

	for i := range orders {
		s.populateOrderDiscount(&orders[i])
	}

	return orders, total, nil
}

func (s *orderService) ListOrdersByCoupon(couponID int, page, limit int) ([]models.Order, int, error) {
	if page <= 0 {
		page = 1
	}
	if limit <= 0 || limit > 100 {
		limit = 10
	}

	orders, total, err := s.orderRepo.GetByCouponID(couponID, page, limit)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to list orders by coupon: %w", err)
	}

	for i := range orders {
		s.populateOrderDiscount(&orders[i])
	}

	return orders, total, nil
}

func (s *orderService) handleManualCouponApplication(couponCode string, orderAmount float64, orderTime time.Time) (*models.Coupon, error) {
	coupon, err := s.couponRepo.GetByCode(couponCode)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("coupon code '%s' not found", couponCode)
		}
		return nil, fmt.Errorf("failed to get coupon: %w", err)
	}

	if err := s.validateCouponForOrder(coupon, orderAmount, orderTime); err != nil {
		return nil, err
	}

	if coupon.UsageMethod == models.UsageMethodAutomatic {
		log.Info().Str("coupon_code", couponCode).Msg("Automatic coupon applied manually")
	}

	return coupon, nil
}

func (s *orderService) handleAutomaticCouponApplication(orderAmount float64, orderTime time.Time) (*models.Coupon, error) {
	coupons, err := s.couponRepo.GetActiveCouponsForAutoApply(orderAmount, orderTime)
	if err != nil {
		return nil, fmt.Errorf("failed to get automatic coupons: %w", err)
	}

	if len(coupons) == 0 {
		return nil, nil
	}

	var bestCoupon *models.Coupon
	var bestDiscount float64

	for _, coupon := range coupons {
		discount, err := s.calculateDiscount(coupon, orderAmount)
		if err != nil {
			log.Warn().Err(err).Int("coupon_id", coupon.ID).Msg("Failed to calculate discount for automatic coupon")
			continue
		}

		if discount > bestDiscount {
			bestDiscount = discount
			bestCoupon = coupon
		}
	}

	return bestCoupon, nil
}

func (s *orderService) validateCouponForOrder(coupon *models.Coupon, orderAmount float64, orderTime time.Time) error {
	if strings.ToUpper(coupon.Status) != "ACTIVE" {
		return fmt.Errorf("coupon '%s' is not active", coupon.CouponCode)
	}

	if orderTime.Before(coupon.ValidFrom) {
		return fmt.Errorf("coupon '%s' is not yet valid (valid from %s)", coupon.CouponCode, coupon.ValidFrom.Format("2006-01-02"))
	}
	if orderTime.After(coupon.ValidUntil) {
		return fmt.Errorf("coupon '%s' has expired (valid until %s)", coupon.CouponCode, coupon.ValidUntil.Format("2006-01-02"))
	}

	if orderAmount < coupon.MinOrderAmount {
		return fmt.Errorf("order amount %.2f is below minimum required amount %.2f for coupon '%s'", orderAmount, coupon.MinOrderAmount, coupon.CouponCode)
	}

	if coupon.MaxUsageCount != nil && coupon.CurrentUsageCount >= *coupon.MaxUsageCount {
		return fmt.Errorf("coupon '%s' has reached its maximum usage limit", coupon.CouponCode)
	}

	return nil
}

func (s *orderService) calculateDiscount(coupon *models.Coupon, orderAmount float64) (float64, error) {
	if coupon.DiscountType == nil {
		return 0, fmt.Errorf("discount type not loaded for coupon")
	}

	var discount float64

	switch coupon.DiscountType.TypeCode {
	case "PERCENT":
		discount = orderAmount * coupon.DiscountValue / 100
	case "FIXED":
		discount = coupon.DiscountValue
	case "FLAT":
		discount = orderAmount - coupon.DiscountValue
		if discount < 0 {
			discount = 0
		}
	default:
		return 0, fmt.Errorf("unsupported discount type: %s", coupon.DiscountType.TypeCode)
	}

	if coupon.MaxDiscountAmount != nil && discount > *coupon.MaxDiscountAmount {
		discount = *coupon.MaxDiscountAmount
	}

	if discount > orderAmount {
		discount = orderAmount
	}

	if discount < 0 {
		discount = 0
	}

	return math.Round(discount*100) / 100, nil
}

func (s *orderService) populateOrderDiscount(order *models.Order) {
	if order == nil {
		return
	}
	if order.AppliedCoupon != nil {
		discount, err := s.calculateDiscount(order.AppliedCoupon, order.OrderAmount)
		if err == nil {
			order.DiscountAmount = discount
		}
	} else {
		order.DiscountAmount = 0
	}
}

```

`coupon-be/pkg/service/product_service.go`:

```go
package service

import (
	"database/sql"
	"fmt"
	"strings"

	"coupon/pkg/db/models"
	"coupon/pkg/repository"
)

type ProductService interface {
	CreateProduct(p *models.Product) (*models.Product, error)
	GetProductByID(id int) (*models.Product, error)
	ListProducts(page, limit int) ([]models.Product, int, error)
}

type productService struct {
	repo repository.ProductRepository
}

func NewProductService(repo repository.ProductRepository) ProductService {
	return &productService{repo: repo}
}

func (s *productService) CreateProduct(p *models.Product) (*models.Product, error) {
	if strings.TrimSpace(p.Name) == "" {
		return nil, fmt.Errorf("invalid name")
	}

	if p.Price <= 0 {
		return nil, fmt.Errorf("invalid price")
	}
	if p.StockQuantity < 0 {
		return nil, fmt.Errorf("invalid stock")
	}
	prod, err := s.repo.Create(p)
	if err != nil {
		return nil, fmt.Errorf("failed to create product: %w", err)
	}
	return prod, nil
}

func (s *productService) GetProductByID(id int) (*models.Product, error) {
	prod, err := s.repo.GetByID(id)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("product not found")
		}
		return nil, fmt.Errorf("failed to get product: %w", err)
	}
	return prod, nil
}

func (s *productService) ListProducts(page, limit int) ([]models.Product, int, error) {
	if page <= 0 {
		page = 1
	}
	if limit <= 0 {
		limit = 10
	}
	offset := (page - 1) * limit
	products, total, err := s.repo.List(offset, limit)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to list products: %w", err)
	}
	return products, total, nil
}

```

`coupon-be/pkg/service/user_service.go`:

```go
package service

import (
	"database/sql"
	"fmt"

	"coupon/pkg/db/models"
	"coupon/pkg/repository"
)

type UserService interface {
	GetUserByID(id int) (*models.User, error)
}

type userService struct {
	repo repository.UserRepository
}

func NewUserService(repo repository.UserRepository) UserService {
	return &userService{repo: repo}
}

func (s *userService) GetUserByID(id int) (*models.User, error) {
	user, err := s.repo.GetByID(id)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("user not found")
		}
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	return user, nil
}

```

`coupon-be/pkg/utils/handle_error.go`:

```go
package utils

import (
	"database/sql"
	"net/http"

	"github.com/labstack/echo/v4"
	"github.com/rs/zerolog/log"
)

type ErrResponse struct {
	Message  string `json:"message"`
	Metadata any    `json:"metadata,omitempty"`
}

func HTTPErrorHandler(err error, c echo.Context) {
	if c.Response().Committed {
		return
	}

	if m, ok := err.(*ValidationError); ok {
		c.JSON(http.StatusBadRequest, m)
	} else if m, ok := err.(*echo.HTTPError); ok {
		switch errValue := m.Message.(type) {
		case string:
			c.JSON(m.Code, ErrResponse{Message: errValue})
		case *ValidationError:
			c.JSON(m.Code, errValue)
		case error:
			c.JSON(m.Code, ErrResponse{Message: errValue.Error()})
		}
	} else {
		log.Err(err).Msg("http error")
		if err == sql.ErrNoRows {
			c.JSON(http.StatusNotFound, ErrResponse{
				Message: "Resource not found",
			})
		} else {
			c.JSON(http.StatusInternalServerError, ErrResponse{
				Message: http.StatusText(http.StatusInternalServerError),
			})
		}
	}
}

```

`coupon-be/pkg/utils/token.go`:

```go
package utils

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"fmt"
	"strconv"
	"strings"
)

func GenerateToken(userID int, role string, secret string) (string, error) {
	payload := fmt.Sprintf("%d:%s", userID, role)
	mac := hmac.New(sha256.New, []byte(secret))
	mac.Write([]byte(payload))
	sig := mac.Sum(nil)
	token := base64.StdEncoding.EncodeToString([]byte(payload)) + "." + fmt.Sprintf("%x", sig)
	return token, nil
}

func ParseToken(token, secret string) (int, string, error) {
	parts := strings.Split(token, ".")
	if len(parts) != 2 {
		return 0, "", fmt.Errorf("invalid token")
	}
	payloadBytes, err := base64.StdEncoding.DecodeString(parts[0])
	if err != nil {
		return 0, "", fmt.Errorf("invalid token")
	}
	mac := hmac.New(sha256.New, []byte(secret))
	mac.Write(payloadBytes)
	expected := fmt.Sprintf("%x", mac.Sum(nil))
	if !hmac.Equal([]byte(expected), []byte(parts[1])) {
		return 0, "", fmt.Errorf("invalid token")
	}
	payloadParts := strings.SplitN(string(payloadBytes), ":", 2)
	if len(payloadParts) != 2 {
		return 0, "", fmt.Errorf("invalid token")
	}
	id, err := strconv.Atoi(payloadParts[0])
	if err != nil {
		return 0, "", fmt.Errorf("invalid token")
	}
	role := payloadParts[1]
	return id, role, nil
}

```

`coupon-be/pkg/utils/validate.go`:

```go
package utils

import (
	"fmt"
	"reflect"
	"regexp"
	"strconv"
	"strings"
	"time"
)

type ValidationError struct {
	Message  string         `json:"message"`
	Metadata map[string]any `json:"metadata"`
}

func (ve ValidationError) Error() string {
	return ve.Message
}

func ValidateStruct(s any) error {
	v := reflect.ValueOf(s)
	if v.Kind() == reflect.Pointer {
		v = v.Elem()
	}

	if v.Kind() != reflect.Struct {
		return &ValidationError{
			Message: "Input must be a struct",
			Metadata: map[string]any{
				"type": v.Kind().String(),
			},
		}
	}

	t := v.Type()
	for i := 0; i < v.NumField(); i++ {
		field := v.Field(i)
		fieldType := t.Field(i)

		if !fieldType.IsExported() {
			continue
		}

		tag := fieldType.Tag.Get("validate")
		if tag == "" {
			continue
		}

		if err := validateField(field, fieldType.Name, tag); err != nil {
			return err
		}
	}

	return nil
}

func validateField(field reflect.Value, fieldName, tag string) error {
	rules := strings.SplitSeq(tag, ",")

	for rule := range rules {
		rule = strings.TrimSpace(rule)
		if rule == "" {
			continue
		}

		if err := applyValidationRule(field, fieldName, rule); err != nil {
			return err
		}
	}

	return nil
}

func applyValidationRule(field reflect.Value, fieldName, rule string) error {
	switch {
	case rule == "required":
		return validateRequired(field, fieldName)
	case strings.HasPrefix(rule, "min="):
		return validateMin(field, fieldName, rule)
	case strings.HasPrefix(rule, "max="):
		return validateMax(field, fieldName, rule)
	case strings.HasPrefix(rule, "gt="):
		return validateGt(field, fieldName, rule)
	case strings.HasPrefix(rule, "gte="):
		return validateGte(field, fieldName, rule)
	case strings.HasPrefix(rule, "lt="):
		return validateLt(field, fieldName, rule)
	case strings.HasPrefix(rule, "lte="):
		return validateLte(field, fieldName, rule)
	case rule == "email":
		return validateEmail(field, fieldName)
	}

	return nil
}

func validateRequired(field reflect.Value, fieldName string) error {
	if field.Kind() == reflect.Pointer {
		if field.IsNil() {
			return &ValidationError{
				Message: fmt.Sprintf("%s is required", fieldName),
				Metadata: map[string]any{
					"field": fieldName,
				},
			}
		}
		field = field.Elem()
	}

	switch field.Kind() {
	case reflect.String:
		if field.String() == "" {
			return &ValidationError{
				Message: fmt.Sprintf("%s is required", fieldName),
				Metadata: map[string]any{
					"field": fieldName,
				},
			}
		}
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
	case reflect.Float32, reflect.Float64:
	case reflect.Slice, reflect.Map, reflect.Array:
		if field.Len() == 0 {
			return &ValidationError{
				Message: fmt.Sprintf("%s is required", fieldName),
				Metadata: map[string]any{
					"field": fieldName,
				},
			}
		}
	case reflect.Struct:
		if field.Type() == reflect.TypeOf(time.Time{}) {
			if field.Interface().(time.Time).IsZero() {
				return &ValidationError{
					Message: fmt.Sprintf("%s is required", fieldName),
					Metadata: map[string]any{
						"field": fieldName,
					},
				}
			}
		}
	}

	return nil
}

func validateMin(field reflect.Value, fieldName, rule string) error {
	minRawStr := strings.TrimPrefix(rule, "min=")
	minStr, err := strconv.ParseFloat(minRawStr, 64)
	if err != nil {
		return fmt.Errorf("invalid min value: %s", minRawStr)
	}

	switch field.Kind() {
	case reflect.String:
		if float64(len(field.String())) < minStr {
			return &ValidationError{
				Message: fmt.Sprintf("%s must be at least %.0f characters long", fieldName, minStr),
				Metadata: map[string]any{
					"field":     fieldName,
					"min_value": minStr,
					"actual":    len(field.String()),
				},
			}
		}
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		if float64(field.Int()) < minStr {
			return &ValidationError{
				Message: fmt.Sprintf("%s must be at least %.0f", fieldName, minStr),
				Metadata: map[string]any{
					"field":     fieldName,
					"min_value": minStr,
					"actual":    field.Int(),
				},
			}
		}
	case reflect.Float32, reflect.Float64:
		if field.Float() < minStr {
			return &ValidationError{
				Message: fmt.Sprintf("%s must be at least %g", fieldName, minStr),
				Metadata: map[string]any{
					"field":     fieldName,
					"min_value": minStr,
					"actual":    field.Float(),
				},
			}
		}
	}

	return nil
}

func validateMax(field reflect.Value, fieldName, rule string) error {
	maxRawStr := strings.TrimPrefix(rule, "max=")
	maxStr, err := strconv.ParseFloat(maxRawStr, 64)
	if err != nil {
		return fmt.Errorf("invalid max value: %s", maxRawStr)
	}

	switch field.Kind() {
	case reflect.String:
		if float64(len(field.String())) > maxStr {
			return &ValidationError{
				Message: fmt.Sprintf("%s must be at most %.0f characters long", fieldName, maxStr),
				Metadata: map[string]any{
					"field":     fieldName,
					"max_value": maxStr,
					"actual":    len(field.String()),
				},
			}
		}
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		if float64(field.Int()) > maxStr {
			return &ValidationError{
				Message: fmt.Sprintf("%s must be at most %.0f", fieldName, maxStr),
				Metadata: map[string]any{
					"field":     fieldName,
					"max_value": maxStr,
					"actual":    field.Int(),
				},
			}
		}
	case reflect.Float32, reflect.Float64:
		if field.Float() > maxStr {
			return &ValidationError{
				Message: fmt.Sprintf("%s must be at most %g", fieldName, maxStr),
				Metadata: map[string]any{
					"field":     fieldName,
					"max_value": maxStr,
					"actual":    field.Float(),
				},
			}
		}
	}

	return nil
}

func validateGt(field reflect.Value, fieldName, rule string) error {
	gtStr := strings.TrimPrefix(rule, "gt=")
	gt, err := strconv.ParseFloat(gtStr, 64)
	if err != nil {
		return fmt.Errorf("invalid gt value: %s", gtStr)
	}

	switch field.Kind() {
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		if float64(field.Int()) <= gt {
			return &ValidationError{
				Message: fmt.Sprintf("%s must be greater than %g", fieldName, gt),
				Metadata: map[string]any{
					"field":    fieldName,
					"gt_value": gt,
					"actual":   field.Int(),
				},
			}
		}
	case reflect.Float32, reflect.Float64:
		if field.Float() <= gt {
			return &ValidationError{
				Message: fmt.Sprintf("%s must be greater than %g", fieldName, gt),
				Metadata: map[string]any{
					"field":    fieldName,
					"gt_value": gt,
					"actual":   field.Float(),
				},
			}
		}
	}

	return nil
}

func validateGte(field reflect.Value, fieldName, rule string) error {
	gteStr := strings.TrimPrefix(rule, "gte=")
	gte, err := strconv.ParseFloat(gteStr, 64)
	if err != nil {
		return fmt.Errorf("invalid gte value: %s", gteStr)
	}

	switch field.Kind() {
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		if float64(field.Int()) < gte {
			return &ValidationError{
				Message: fmt.Sprintf("%s must be greater than or equal to %g", fieldName, gte),
				Metadata: map[string]any{
					"field":     fieldName,
					"gte_value": gte,
					"actual":    field.Int(),
				},
			}
		}
	case reflect.Float32, reflect.Float64:
		if field.Float() < gte {
			return &ValidationError{
				Message: fmt.Sprintf("%s must be greater than or equal to %g", fieldName, gte),
				Metadata: map[string]any{
					"field":     fieldName,
					"gte_value": gte,
					"actual":    field.Float(),
				},
			}
		}
	}

	return nil
}

func validateLt(field reflect.Value, fieldName, rule string) error {
	ltStr := strings.TrimPrefix(rule, "lt=")
	lt, err := strconv.ParseFloat(ltStr, 64)
	if err != nil {
		return fmt.Errorf("invalid lt value: %s", ltStr)
	}

	switch field.Kind() {
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		if float64(field.Int()) >= lt {
			return &ValidationError{
				Message: fmt.Sprintf("%s must be less than %g", fieldName, lt),
				Metadata: map[string]any{
					"field":    fieldName,
					"lt_value": lt,
					"actual":   field.Int(),
				},
			}
		}
	case reflect.Float32, reflect.Float64:
		if field.Float() >= lt {
			return &ValidationError{
				Message: fmt.Sprintf("%s must be less than %g", fieldName, lt),
				Metadata: map[string]any{
					"field":    fieldName,
					"lt_value": lt,
					"actual":   field.Float(),
				},
			}
		}
	}

	return nil
}

func validateLte(field reflect.Value, fieldName, rule string) error {
	lteStr := strings.TrimPrefix(rule, "lte=")
	lte, err := strconv.ParseFloat(lteStr, 64)
	if err != nil {
		return fmt.Errorf("invalid lte value: %s", lteStr)
	}

	switch field.Kind() {
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		if float64(field.Int()) > lte {
			return &ValidationError{
				Message: fmt.Sprintf("%s must be less than or equal to %g", fieldName, lte),
				Metadata: map[string]any{
					"field":     fieldName,
					"lte_value": lte,
					"actual":    field.Int(),
				},
			}
		}
	case reflect.Float32, reflect.Float64:
		if field.Float() > lte {
			return &ValidationError{
				Message: fmt.Sprintf("%s must be less than or equal to %g", fieldName, lte),
				Metadata: map[string]any{
					"field":     fieldName,
					"lte_value": lte,
					"actual":    field.Float(),
				},
			}
		}
	}

	return nil
}

func validateEmail(field reflect.Value, fieldName string) error {
	if field.Kind() != reflect.String {
		return nil
	}

	email := field.String()
	if email == "" {
		return nil
	}

	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	if !emailRegex.MatchString(email) {
		return &ValidationError{
			Message: fmt.Sprintf("%s must be a valid email address", fieldName),
			Metadata: map[string]any{
				"field": fieldName,
				"value": email,
			},
		}
	}

	return nil
}

```