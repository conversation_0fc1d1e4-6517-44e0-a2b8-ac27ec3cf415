#!/bin/bash

<PERSON>AF<PERSON>_CONTAINER="kafka"
BOOTSTRAP_SERVER="kafka:29092"

echo "Waiting for <PERSON><PERSON>ka to be ready..."
sleep 10

# Check if Kafka is accessible
docker exec $KAFKA_CONTAINER kafka-topics --bootstrap-server $BOOTSTRAP_SERVER --list > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "Error: Kafka is not accessible. Please ensure Kafka container is running."
    exit 1
fi

echo "Creating Kafka topics..."

# Voucher Events Topic
echo "Creating voucher-events topic..."
docker exec $KAFKA_CONTAINER kafka-topics \
  --create \
  --bootstrap-server $BOOTSTRAP_SERVER \
  --topic voucher-events \
  --partitions 3 \
  --replication-factor 1 \
  --config retention.ms=604800000 \
  --config segment.ms=86400000 \
  --if-not-exists

# Order Events Topic
echo "Creating order-events topic..."
docker exec $KAFKA_CONTAINER kafka-topics \
  --create \
  --bootstrap-server $BOOTSTRAP_SERVER \
  --topic order-events \
  --partitions 3 \
  --replication-factor 1 \
  --config retention.ms=604800000 \
  --config segment.ms=86400000 \
  --if-not-exists

# User Events Topic
echo "Creating user-events topic..."
docker exec $KAFKA_CONTAINER kafka-topics \
  --create \
  --bootstrap-server $BOOTSTRAP_SERVER \
  --topic user-events \
  --partitions 3 \
  --replication-factor 1 \
  --config retention.ms=604800000 \
  --config segment.ms=86400000 \
  --if-not-exists

# Notification Events Topic
echo "Creating notification-events topic..."
docker exec $KAFKA_CONTAINER kafka-topics \
  --create \
  --bootstrap-server $BOOTSTRAP_SERVER \
  --topic notification-events \
  --partitions 3 \
  --replication-factor 1 \
  --config retention.ms=259200000 \
  --config segment.ms=86400000 \
  --if-not-exists

echo "Topics created successfully!"

# List all topics to verify
echo "Current topics:"
docker exec $KAFKA_CONTAINER kafka-topics --bootstrap-server $BOOTSTRAP_SERVER --list

echo "Topic details:"
for topic in voucher-events order-events user-events notification-events; do
    echo "=== $topic ==="
    docker exec $KAFKA_CONTAINER kafka-topics --bootstrap-server $BOOTSTRAP_SERVER --describe --topic $topic
done
