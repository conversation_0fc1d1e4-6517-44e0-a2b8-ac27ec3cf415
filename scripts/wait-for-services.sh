#!/bin/bash

echo "Waiting for infrastructure services to be ready..."

# Function to check if a service is healthy
check_service_health() {
    local service_name=$1
    local max_attempts=30
    local attempt=1
    
    echo "Checking $service_name health..."
    
    while [ $attempt -le $max_attempts ]; do
        if docker-compose -f infrastructure-compose.yml ps $service_name | grep -q "healthy\|Up"; then
            echo "$service_name is ready!"
            return 0
        fi
        
        echo "Attempt $attempt/$max_attempts: $service_name not ready yet, waiting..."
        sleep 10
        attempt=$((attempt + 1))
    done
    
    echo "Error: $service_name failed to become ready within expected time"
    return 1
}

# Check Zookeeper
check_service_health "zookeeper"
if [ $? -ne 0 ]; then
    exit 1
fi

# Check Kafka
check_service_health "kafka"
if [ $? -ne 0 ]; then
    exit 1
fi

# Additional Kafka connectivity test
echo "Testing Kafka connectivity..."
max_attempts=10
attempt=1

while [ $attempt -le $max_attempts ]; do
    if docker exec kafka kafka-topics --bootstrap-server kafka:29092 --list > /dev/null 2>&1; then
        echo "Kafka is accessible!"
        break
    fi
    
    echo "Attempt $attempt/$max_attempts: Kafka not accessible yet, waiting..."
    sleep 5
    attempt=$((attempt + 1))
done

if [ $attempt -gt $max_attempts ]; then
    echo "Error: Kafka is not accessible"
    exit 1
fi

echo "All infrastructure services are ready!"
echo "You can now:"
echo "1. Create Kafka topics: ./scripts/create-topics.sh"
echo "2. Access Kafka UI at: http://localhost:8090"
echo "3. Access Jaeger at: http://localhost:16686"
echo "4. Access Adminer at: http://localhost:8091"
