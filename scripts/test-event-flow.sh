#!/bin/bash

# Test Event Flow Script
# This script tests the complete event-driven notification flow

set -e

API_BASE="http://localhost:8080/api/v1"
KAFKA_UI="http://localhost:8090"

echo "🧪 Testing Event-Driven Notification Flow"
echo "=========================================="

# Function to check if service is healthy
check_service_health() {
    local service_name=$1
    local port=$2
    
    echo "Checking $service_name health..."
    if curl -s -f "http://localhost:$port/health" > /dev/null; then
        echo "✅ $service_name is healthy"
    else
        echo "❌ $service_name is not healthy"
        exit 1
    fi
}

# Function to check Kafka topic
check_kafka_topic() {
    local topic=$1
    echo "Checking Kafka topic: $topic"
    
    # This is a simplified check - in real implementation you'd use Kafka CLI tools
    echo "📊 Check topic activity in Kafka UI: $KAFKA_UI"
}

# Function to wait for a moment
wait_for_processing() {
    echo "⏳ Waiting for event processing..."
    sleep 3
}

echo ""
echo "1️⃣ Checking Service Health"
echo "=========================="

check_service_health "API Gateway" 8080
check_service_health "Auth Service" 8081
check_service_health "User Service" 8082
check_service_health "Product Service" 8083
check_service_health "Voucher Service" 8084
check_service_health "Order Service" 8085
check_service_health "Notification Service" 8086

echo ""
echo "2️⃣ Testing User Registration Flow"
echo "================================="

# Register a new user
echo "Creating test user..."
USER_RESPONSE=$(curl -s -X POST "$API_BASE/users/register" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "name": "Test User"
  }')

echo "User registration response: $USER_RESPONSE"

# Check if user.created event was published
check_kafka_topic "user-events"
wait_for_processing

echo ""
echo "3️⃣ Testing User Login Flow"
echo "=========================="

# Login user
echo "Logging in test user..."
LOGIN_RESPONSE=$(curl -s -X POST "$API_BASE/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }')

echo "Login response: $LOGIN_RESPONSE"

# Extract JWT token (this would need proper JSON parsing in real implementation)
JWT_TOKEN="dummy-token-for-testing"

# Check if user.login event was published
check_kafka_topic "user-events"
wait_for_processing

echo ""
echo "4️⃣ Testing Voucher Creation Flow"
echo "================================"

# Create a voucher
echo "Creating test voucher..."
VOUCHER_RESPONSE=$(curl -s -X POST "$API_BASE/vouchers" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -d '{
    "title": "Test Discount",
    "voucher_code": "TEST10",
    "discount_value": 10.0,
    "discount_type": "PERCENTAGE",
    "user_eligibility": "ALL",
    "valid_from": "2024-01-01T00:00:00Z",
    "valid_until": "2024-12-31T23:59:59Z"
  }')

echo "Voucher creation response: $VOUCHER_RESPONSE"

# Check if voucher.created event was published
check_kafka_topic "voucher-events"
wait_for_processing

echo ""
echo "5️⃣ Testing Order Creation Flow"
echo "=============================="

# Create an order with voucher
echo "Creating test order with voucher..."
ORDER_RESPONSE=$(curl -s -X POST "$API_BASE/orders" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -d '{
    "items": [
      {
        "product_id": 1,
        "quantity": 2,
        "price": 25.0
      }
    ],
    "voucher_code": "TEST10"
  }')

echo "Order creation response: $ORDER_RESPONSE"

# Check if order.created and voucher.applied events were published
check_kafka_topic "order-events"
check_kafka_topic "voucher-events"
wait_for_processing

echo ""
echo "6️⃣ Testing Notification Generation"
echo "=================================="

# Check notifications for the user
echo "Checking generated notifications..."
NOTIFICATIONS_RESPONSE=$(curl -s -X GET "$API_BASE/notifications" \
  -H "Authorization: Bearer $JWT_TOKEN")

echo "Notifications response: $NOTIFICATIONS_RESPONSE"

echo ""
echo "7️⃣ Kafka Topic Activity Summary"
echo "==============================="

echo "📊 Check the following in Kafka UI ($KAFKA_UI):"
echo "   - voucher-events topic should have voucher.created and voucher.used events"
echo "   - order-events topic should have order.created and order.voucher_applied events"
echo "   - user-events topic should have user.created and user.login events"
echo "   - notification-events topic should have notification processing events"

echo ""
echo "8️⃣ Database Verification"
echo "========================"

echo "📊 Check the following in Adminer (http://localhost:8091):"
echo "   - notification_db.notifications table should have new notifications"
echo "   - notification_db.notification_delivery_logs should show delivery attempts"
echo "   - notification_db.notification_templates should have default templates"

echo ""
echo "9️⃣ Service Logs"
echo "==============="

echo "📊 Check service logs for event processing:"
echo "   docker logs notification-service | grep 'Processing.*event'"
echo "   docker logs voucher-service | grep 'Published.*event'"
echo "   docker logs order-service | grep 'Published.*event'"
echo "   docker logs user-service | grep 'Published.*event'"

echo ""
echo "🎉 Event Flow Test Complete!"
echo "============================"

echo ""
echo "Next Steps:"
echo "1. Verify events in Kafka UI: $KAFKA_UI"
echo "2. Check notifications in database via Adminer: http://localhost:8091"
echo "3. Monitor service logs for event processing"
echo "4. Test notification delivery channels (email, push, SMS)"

echo ""
echo "Manual Testing Commands:"
echo "========================"
echo ""
echo "# Check Kafka topics"
echo "docker exec kafka kafka-topics --bootstrap-server kafka:29092 --list"
echo ""
echo "# Check consumer groups"
echo "docker exec kafka kafka-consumer-groups --bootstrap-server kafka:29092 --list"
echo ""
echo "# Monitor specific topic"
echo "docker exec kafka kafka-console-consumer --bootstrap-server kafka:29092 --topic voucher-events --from-beginning"
echo ""
echo "# Check notification service logs"
echo "docker logs notification-service -f"
echo ""
echo "# Check database"
echo "# Go to http://localhost:8091, connect to postgres-notification with credentials from docker-compose.yml"
