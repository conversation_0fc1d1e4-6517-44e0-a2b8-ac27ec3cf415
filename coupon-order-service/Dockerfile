# Stage 1: Builder
FROM golang:1.24-alpine AS builder

ARG GITLAB_USER
ARG GITLAB_TOKEN
ENV GOPRIVATE=gitlab.zalopay.vn

WORKDIR /app

COPY go.mod go.sum ./

RUN apk add --no-cache git

RUN if [ -n "$GITLAB_TOKEN" ]; then \
      git config --global url."https://${GITLAB_USER}:${GITLAB_TOKEN}@gitlab.zalopay.vn/".insteadOf "https://gitlab.zalopay.vn/"; \
    fi && \
    go mod download

COPY . .

RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o order-server ./cmd/server

# Stage 2: Final
FROM alpine:latest

WORKDIR /app

COPY --from=builder /app/order-server .

COPY config ./config

EXPOSE 8080
EXPOSE 50051
EXPOSE 2112

CMD ["./order-server"]
