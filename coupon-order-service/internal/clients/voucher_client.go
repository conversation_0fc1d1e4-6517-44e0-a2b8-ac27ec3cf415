package clients

import (
	"context"
	"fmt"
	"time"

	"gitlab.zalopay.vn/phunn4/coupon-order-service/internal/model"
	"gitlab.zalopay.vn/phunn4/coupon-order-service/internal/service"
	proto_common_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/common/v1"
	proto_voucher_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/voucher/v1"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	shared_grpc "gitlab.zalopay.vn/phunn4/coupon-shared-libs/grpc"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/metrics"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type VoucherClient struct {
	client proto_voucher_v1.VoucherServiceClient
	conn   *shared_grpc.Client
	logger *logging.Logger
}

func NewVoucherClient(target string, cfg *config.GRPCConfig, logger *logging.Logger, metrics *metrics.Metrics, clientID, clientKey string) (*VoucherClient, error) {
	client, err := shared_grpc.NewAuthenticatedClient(target, cfg, logger, metrics, clientID, clientKey)
	if err != nil {
		return nil, fmt.Errorf("failed to create shared gRPC client for voucher service: %w", err)
	}

	return &VoucherClient{
		client: proto_voucher_v1.NewVoucherServiceClient(client.Conn),
		conn:   client,
		logger: logger,
	}, nil
}

func (c *VoucherClient) Close() {
	c.conn.Close()
}

func (c *VoucherClient) CheckVoucherEligibility(ctx context.Context, userID uint64, voucherCode string, orderAmount float64, orderTimestamp time.Time, items []model.OrderItem) (*service.VoucherEligibilityResponse, error) {
	log := c.logger.WithContext(ctx)

	// Convert order items to CartItem proto format
	var cartItems []*proto_voucher_v1.CartItem
	for _, item := range items {
		cartItems = append(cartItems, &proto_voucher_v1.CartItem{
			ProductId: &item.ProductID,
			Quantity:  item.Quantity,
			Price:     item.Price,
		})
	}

	req := &proto_voucher_v1.CheckVoucherEligibilityRequest{
		Metadata: &proto_common_v1.RequestMetadata{
			RequestId:   fmt.Sprintf("order-service-%d", time.Now().UnixNano()),
			UserId:      userID,
			Timestamp:   timestamppb.Now(),
			ServiceName: "order-service",
		},
		VoucherCode:    voucherCode,
		UserId:         userID,
		OrderAmount:    orderAmount,
		OrderTimestamp: timestamppb.New(orderTimestamp),
		CartItems:      cartItems,
	}

	ctx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	resp, err := c.client.CheckVoucherEligibility(ctx, req)
	if err != nil {
		log.Errorf("Failed to check voucher eligibility: %v", err)
		return nil, err
	}

	if resp.Error != nil {
		return &service.VoucherEligibilityResponse{
			Eligible: false,
			Message:  resp.Error.Message,
		}, nil
	}

	result := &service.VoucherEligibilityResponse{
		Eligible:       resp.Eligible,
		DiscountAmount: resp.DiscountAmount,
		Message:        resp.Message,
	}

	// voucher_id is uint64, not *uint64 in the response
	result.VoucherID = &resp.VoucherId

	// VoucherCode is not available in CheckVoucherEligibilityResponse
	// We can use the original voucherCode from the request
	result.VoucherCode = &voucherCode

	return result, nil
}

func (c *VoucherClient) GetEligibleAutoVouchers(ctx context.Context, userID uint64, orderAmount float64, orderTimestamp time.Time, items []model.OrderItem) ([]*service.VoucherEligibilityResponse, error) {
	log := c.logger.WithContext(ctx)

	// Convert order items to CartItem proto format
	var cartItems []*proto_voucher_v1.CartItem
	for _, item := range items {
		cartItems = append(cartItems, &proto_voucher_v1.CartItem{
			ProductId: &item.ProductID,
			Quantity:  item.Quantity,
			Price:     item.Price,
		})
	}

	req := &proto_voucher_v1.ListAutoEligibleVouchersRequest{
		Metadata: &proto_common_v1.RequestMetadata{
			RequestId:   fmt.Sprintf("order-service-%d", time.Now().UnixNano()),
			UserId:      userID,
			Timestamp:   timestamppb.Now(),
			ServiceName: "order-service",
		},
		UserId:         userID,
		OrderAmount:    orderAmount,
		OrderTimestamp: timestamppb.New(orderTimestamp),
		CartItems:      cartItems,
	}

	ctx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	resp, err := c.client.ListAutoEligibleVouchers(ctx, req)
	if err != nil {
		log.Errorf("Failed to get eligible auto vouchers: %v", err)
		return nil, err
	}

	if resp.Error != nil {
		log.Warnf("Error getting eligible auto vouchers: %s", resp.Error.Message)
		return nil, fmt.Errorf(resp.Error.Message)
	}

	var results []*service.VoucherEligibilityResponse
	for _, voucherInfo := range resp.Vouchers {
		if voucherInfo.Eligible && voucherInfo.Voucher != nil {
			result := &service.VoucherEligibilityResponse{
				Eligible:       voucherInfo.Eligible,
				DiscountAmount: voucherInfo.DiscountAmount,
				Message:        "Auto voucher eligible",
			}

			// Set voucher ID and code from the voucher object
			result.VoucherID = &voucherInfo.Voucher.Id
			result.VoucherCode = &voucherInfo.Voucher.VoucherCode

			results = append(results, result)
		}
	}

	return results, nil
}

func (c *VoucherClient) IncrementVoucherUsage(ctx context.Context, voucherID, userID uint64, orderAmount float64, orderTimestamp time.Time) error {
	log := c.logger.WithContext(ctx)

	req := &proto_voucher_v1.IncrementVoucherUsageRequest{
		Metadata: &proto_common_v1.RequestMetadata{
			RequestId:   fmt.Sprintf("order-service-%d", time.Now().UnixNano()),
			UserId:      userID,
			Timestamp:   timestamppb.Now(),
			ServiceName: "order-service",
		},
		VoucherId:      voucherID,
		UserId:         userID,
		OrderAmount:    orderAmount,
		OrderTimestamp: timestamppb.New(orderTimestamp),
	}

	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	resp, err := c.client.IncrementVoucherUsage(ctx, req)
	if err != nil {
		log.Errorf("Failed to increment voucher usage: %v", err)
		return err
	}

	if resp.Error != nil {
		log.Errorf("Error incrementing voucher usage: %s", resp.Error.Message)
		return fmt.Errorf(resp.Error.Message)
	}

	if !resp.Success {
		log.Errorf("Failed to increment voucher usage: %s", resp.Message)
		return fmt.Errorf("failed to increment voucher usage: %s", resp.Message)
	}

	log.Infof("Successfully incremented usage for voucher %d to %d", voucherID, resp.NewUsageCount)
	return nil
}
