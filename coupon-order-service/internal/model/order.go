package model

import (
	"time"
)

// Order represents an order in the system
type Order struct {
	ID                 uint64    `gorm:"primaryKey;autoIncrement"`
	UserID             uint64    `gorm:"not null;index"`
	OrderAmount        float64   `gorm:"type:decimal(10,2);not null"`
	AppliedVoucherID   *uint64   `gorm:"index"`
	DiscountAmount     float64   `gorm:"type:decimal(10,2);default:0"`
	CalculationStatus  string    `gorm:"type:varchar(50);not null;default:'SUCCESS'"`
	CalculationMessage string    `gorm:"type:text;default:''"`
	CreatedAt          time.Time `gorm:"not null"`
	UpdatedAt          time.Time `gorm:"not null"`

	// Relationships
	Items []OrderItem `gorm:"foreignKey:OrderID;constraint:OnDelete:CASCADE"`
}

func (Order) TableName() string { return "orders" }

// OrderItem represents an item within an order
type OrderItem struct {
	ID        uint64    `gorm:"primaryKey;autoIncrement"`
	OrderID   uint64    `gorm:"not null;index"`
	ProductID uint64    `gorm:"not null;index"`
	Quantity  int32     `gorm:"not null"`
	Price     float64   `gorm:"type:decimal(10,2);not null"`
	CreatedAt time.Time `gorm:"not null"`
}

func (OrderItem) TableName() string { return "order_items" }

// CreateOrderRequest represents the request to create an order
type CreateOrderRequest struct {
	UserID         uint64      `json:"user_id" validate:"required"`
	OrderAmount    float64     `json:"order_amount" validate:"required,gt=0"`
	OrderTimestamp time.Time   `json:"order_timestamp" validate:"required"`
	VoucherCode    *string     `json:"voucher_code"`
	Items          []OrderItem `json:"items" validate:"required,min=1"`
}

// OrderCalculationResponse represents the response from order discount calculation (internal use)
type OrderCalculationResponse struct {
	OrderAmount        float64 `json:"order_amount"`
	VoucherCode        *string `json:"voucher_code"`
	AppliedVoucherID   *uint64 `json:"applied_voucher_id"`
	AppliedVoucherCode *string `json:"applied_voucher_code"`
	DiscountAmount     float64 `json:"discount_amount"`
	FinalAmount        float64 `json:"final_amount"`
	Status             string  `json:"status"`
	Message            string  `json:"message"`
}

// ListOrdersRequest represents the request to list orders
type ListOrdersRequest struct {
	UserID *uint64 `json:"user_id"`
	Page   int32   `json:"page" validate:"min=1"`
	Limit  int32   `json:"limit" validate:"min=1,max=100"`
	Search *string `json:"search"`
}

// ListOrdersByVoucherRequest represents the request to list orders by voucher
type ListOrdersByVoucherRequest struct {
	VoucherID uint64 `json:"voucher_id" validate:"required"`
	Page      int32  `json:"page" validate:"min=1"`
	Limit     int32  `json:"limit" validate:"min=1,max=100"`
}

// GetUserOrderCountRequest represents the request to get user order count
type GetUserOrderCountRequest struct {
	UserID uint64 `json:"user_id" validate:"required"`
}

// GetUserVoucherUsageCountRequest represents the request to get user voucher usage count
type GetUserVoucherUsageCountRequest struct {
	UserID    uint64 `json:"user_id" validate:"required"`
	VoucherID uint64 `json:"voucher_id" validate:"required"`
}

// UpdateOrderStatusRequest represents the request to update order status
type UpdateOrderStatusRequest struct {
	OrderID uint64 `json:"order_id" validate:"required"`
	Status  string `json:"status" validate:"required"`
}

// OrderWithPagination represents paginated order results
type OrderWithPagination struct {
	Orders      []Order `json:"orders"`
	Total       int64   `json:"total"`
	Page        int32   `json:"page"`
	Limit       int32   `json:"limit"`
	TotalPages  int32   `json:"total_pages"`
	HasNext     bool    `json:"has_next"`
	HasPrevious bool    `json:"has_previous"`
}

// NewOrder creates a new order instance
func NewOrder(userID uint64, orderAmount float64, voucherID *uint64, discountAmount float64, calculationStatus, calculationMessage string) *Order {
	return &Order{
		UserID:             userID,
		OrderAmount:        orderAmount,
		AppliedVoucherID:   voucherID,
		DiscountAmount:     discountAmount,
		CalculationStatus:  calculationStatus,
		CalculationMessage: calculationMessage,
		CreatedAt:          time.Now(),
		UpdatedAt:          time.Now(),
	}
}

// NewOrderItem creates a new order item instance
func NewOrderItem(orderID, productID uint64, quantity int32, price float64) *OrderItem {
	return &OrderItem{
		OrderID:   orderID,
		ProductID: productID,
		Quantity:  quantity,
		Price:     price,
		CreatedAt: time.Now(),
	}
}
