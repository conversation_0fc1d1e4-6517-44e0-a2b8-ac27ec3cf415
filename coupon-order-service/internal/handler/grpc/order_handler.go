package handler

import (
	"context"
	"time"

	app_errors "gitlab.zalopay.vn/phunn4/coupon-shared-libs/errors"

	proto_common_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/common/v1"
	proto_order_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/order/v1"

	"gitlab.zalopay.vn/phunn4/coupon-order-service/internal/model"
	"gitlab.zalopay.vn/phunn4/coupon-order-service/internal/service"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type OrderServer struct {
	proto_order_v1.UnimplementedOrderServiceServer
	svc service.OrderService
}

func NewOrderServer(svc service.OrderService) *OrderServer {
	return &OrderServer{svc: svc}
}

// Helper functions for proto conversion
func convertOrderToProto(order *model.Order) *proto_order_v1.Order {
	protoOrder := &proto_order_v1.Order{
		Id:                 order.ID,
		UserId:             order.UserID,
		OrderAmount:        order.OrderAmount,
		CalculationStatus:  order.CalculationStatus,
		CalculationMessage: order.CalculationMessage,
		CreatedAt:          timestamppb.New(order.CreatedAt),
		UpdatedAt:          timestamppb.New(order.UpdatedAt),
	}

	if order.AppliedVoucherID != nil {
		protoOrder.AppliedVoucherId = order.AppliedVoucherID
	}

	protoOrder.DiscountAmount = &order.DiscountAmount

	// Convert order items
	for _, item := range order.Items {
		protoOrder.Items = append(protoOrder.Items, &proto_order_v1.OrderItem{
			ProductId: item.ProductID,
			Quantity:  item.Quantity,
			Price:     item.Price,
		})
	}

	return protoOrder
}

func convertOrderItemsFromProto(protoItems []*proto_order_v1.OrderItem) []model.OrderItem {
	var items []model.OrderItem
	for _, protoItem := range protoItems {
		items = append(items, model.OrderItem{
			ProductID: protoItem.ProductId,
			Quantity:  protoItem.Quantity,
			Price:     protoItem.Price,
			CreatedAt: time.Now(),
		})
	}
	return items
}

func convertPaginationToProto(pagination *model.OrderWithPagination) *proto_common_v1.PaginationResponse {
	return &proto_common_v1.PaginationResponse{
		CurrentPage: pagination.Page,
		PageSize:    pagination.Limit,
		TotalItems:  pagination.Total,
		TotalPages:  pagination.TotalPages,
		HasNext:     pagination.HasNext,
		HasPrevious: pagination.HasPrevious,
	}
}

func (s *OrderServer) CreateOrder(ctx context.Context, req *proto_order_v1.CreateOrderRequest) (*proto_order_v1.CreateOrderResponse, error) {
	items := convertOrderItemsFromProto(req.Items)

	createReq := &model.CreateOrderRequest{
		UserID:         req.UserId,
		OrderAmount:    req.OrderAmount,
		OrderTimestamp: req.OrderTimestamp.AsTime(),
		Items:          items,
	}

	if req.VoucherCode != nil {
		createReq.VoucherCode = req.VoucherCode
	}

	order, err := s.svc.CreateOrder(ctx, createReq)
	if err != nil {
		return &proto_order_v1.CreateOrderResponse{
			Error: &proto_common_v1.ServiceError{
				Code:    proto_common_v1.StatusCode_STATUS_CODE_INTERNAL,
				Message: err.Error(),
			},
		}, app_errors.ToGRPCError(err)
	}

	return &proto_order_v1.CreateOrderResponse{
		Order: convertOrderToProto(order),
	}, nil
}

func (s *OrderServer) GetOrder(ctx context.Context, req *proto_order_v1.GetOrderRequest) (*proto_order_v1.GetOrderResponse, error) {
	order, err := s.svc.GetOrder(ctx, req.OrderId)
	if err != nil {
		return &proto_order_v1.GetOrderResponse{
			Error: &proto_common_v1.ServiceError{
				Code:    proto_common_v1.StatusCode_STATUS_CODE_NOT_FOUND,
				Message: err.Error(),
			},
		}, app_errors.ToGRPCError(err)
	}

	return &proto_order_v1.GetOrderResponse{
		Order: convertOrderToProto(order),
	}, nil
}

func (s *OrderServer) UpdateOrderStatus(ctx context.Context, req *proto_order_v1.UpdateOrderStatusRequest) (*proto_order_v1.UpdateOrderStatusResponse, error) {
	updateReq := &model.UpdateOrderStatusRequest{
		OrderID: req.OrderId,
		Status:  req.Status,
	}

	order, err := s.svc.UpdateOrderStatus(ctx, updateReq)
	if err != nil {
		return &proto_order_v1.UpdateOrderStatusResponse{
			Error: &proto_common_v1.ServiceError{
				Code:    proto_common_v1.StatusCode_STATUS_CODE_INTERNAL,
				Message: err.Error(),
			},
		}, app_errors.ToGRPCError(err)
	}

	return &proto_order_v1.UpdateOrderStatusResponse{
		Order: convertOrderToProto(order),
	}, nil
}

func (s *OrderServer) ListOrders(ctx context.Context, req *proto_order_v1.ListOrdersRequest) (*proto_order_v1.ListOrdersResponse, error) {
	listReq := &model.ListOrdersRequest{
		Page:  req.Pagination.Page,
		Limit: req.Pagination.PageSize,
	}

	if req.UserId != nil {
		listReq.UserID = req.UserId
	}

	if req.Search != nil {
		listReq.Search = req.Search
	}

	result, err := s.svc.ListOrders(ctx, listReq)
	if err != nil {
		return &proto_order_v1.ListOrdersResponse{
			Error: &proto_common_v1.ServiceError{
				Code:    proto_common_v1.StatusCode_STATUS_CODE_INTERNAL,
				Message: err.Error(),
			},
		}, app_errors.ToGRPCError(err)
	}

	var protoOrders []*proto_order_v1.Order
	for _, order := range result.Orders {
		protoOrders = append(protoOrders, convertOrderToProto(&order))
	}

	return &proto_order_v1.ListOrdersResponse{
		Orders:     protoOrders,
		Pagination: convertPaginationToProto(result),
	}, nil
}

func (s *OrderServer) ListOrdersByVoucher(ctx context.Context, req *proto_order_v1.ListOrdersByVoucherRequest) (*proto_order_v1.ListOrdersByVoucherResponse, error) {
	listReq := &model.ListOrdersByVoucherRequest{
		VoucherID: req.VoucherId,
		Page:      req.Pagination.Page,
		Limit:     req.Pagination.PageSize,
	}

	result, err := s.svc.ListOrdersByVoucher(ctx, listReq)
	if err != nil {
		return &proto_order_v1.ListOrdersByVoucherResponse{
			Error: &proto_common_v1.ServiceError{
				Code:    proto_common_v1.StatusCode_STATUS_CODE_INTERNAL,
				Message: err.Error(),
			},
		}, app_errors.ToGRPCError(err)
	}

	var protoOrders []*proto_order_v1.Order
	for _, order := range result.Orders {
		protoOrders = append(protoOrders, convertOrderToProto(&order))
	}

	return &proto_order_v1.ListOrdersByVoucherResponse{
		Orders:     protoOrders,
		Pagination: convertPaginationToProto(result),
	}, nil
}

func (s *OrderServer) GetUserOrderCount(ctx context.Context, req *proto_order_v1.GetUserOrderCountRequest) (*proto_order_v1.GetUserOrderCountResponse, error) {
	count, err := s.svc.GetUserOrderCount(ctx, req.UserId)
	if err != nil {
		return &proto_order_v1.GetUserOrderCountResponse{
			Error: &proto_common_v1.ServiceError{
				Code:    proto_common_v1.StatusCode_STATUS_CODE_INTERNAL,
				Message: err.Error(),
			},
		}, app_errors.ToGRPCError(err)
	}

	return &proto_order_v1.GetUserOrderCountResponse{
		OrderCount: count,
	}, nil
}

func (s *OrderServer) GetUserVoucherUsageCount(ctx context.Context, req *proto_order_v1.GetUserVoucherUsageCountRequest) (*proto_order_v1.GetUserVoucherUsageCountResponse, error) {
	count, err := s.svc.GetUserVoucherUsageCount(ctx, req.UserId, req.VoucherId)
	if err != nil {
		return &proto_order_v1.GetUserVoucherUsageCountResponse{
			Error: &proto_common_v1.ServiceError{
				Code:    proto_common_v1.StatusCode_STATUS_CODE_INTERNAL,
				Message: err.Error(),
			},
		}, app_errors.ToGRPCError(err)
	}

	return &proto_order_v1.GetUserVoucherUsageCountResponse{
		UsageCount: count,
	}, nil
}

func (s *OrderServer) HealthCheck(ctx context.Context, req *proto_common_v1.HealthCheckRequest) (*proto_common_v1.HealthCheckResponse, error) {
	return &proto_common_v1.HealthCheckResponse{
		Status: proto_common_v1.HealthCheckResponse_SERVING_STATUS_SERVING,
	}, nil
}
