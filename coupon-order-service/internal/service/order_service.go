package service

import (
	"context"
	"errors"
	"fmt"
	"time"

	"gorm.io/gorm"

	"gitlab.zalopay.vn/phunn4/coupon-order-service/internal/model"
	"gitlab.zalopay.vn/phunn4/coupon-order-service/internal/repository"
	app_errors "gitlab.zalopay.vn/phunn4/coupon-shared-libs/errors"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
)

// VoucherClient interface for voucher service integration
type VoucherClient interface {
	CheckVoucherEligibility(ctx context.Context, userID uint64, voucherCode string, orderAmount float64, orderTimestamp time.Time, items []model.OrderItem) (*VoucherEligibilityResponse, error)
	GetEligibleAutoVouchers(ctx context.Context, userID uint64, orderAmount float64, orderTimestamp time.Time, items []model.OrderItem) ([]*VoucherEligibilityResponse, error)
	IncrementVoucherUsage(ctx context.Context, voucherID, userID uint64, orderAmount float64, orderTimestamp time.Time) error
}

// VoucherEligibilityResponse represents the response from voucher eligibility check
type VoucherEligibilityResponse struct {
	Eligible       bool    `json:"eligible"`
	VoucherID      *uint64 `json:"voucher_id"`
	VoucherCode    *string `json:"voucher_code"`
	DiscountAmount float64 `json:"discount_amount"`
	Message        string  `json:"message"`
}

type OrderService interface {
	CreateOrder(ctx context.Context, req *model.CreateOrderRequest) (*model.Order, error)
	GetOrder(ctx context.Context, orderID uint64) (*model.Order, error)
	ListOrders(ctx context.Context, req *model.ListOrdersRequest) (*model.OrderWithPagination, error)
	ListOrdersByVoucher(ctx context.Context, req *model.ListOrdersByVoucherRequest) (*model.OrderWithPagination, error)
	GetUserOrderCount(ctx context.Context, userID uint64) (int64, error)
	GetUserVoucherUsageCount(ctx context.Context, userID, voucherID uint64) (int32, error)
	UpdateOrderStatus(ctx context.Context, req *model.UpdateOrderStatusRequest) (*model.Order, error)
}

type orderService struct {
	repo          repository.OrderRepository
	voucherClient VoucherClient
	logger        *logging.Logger
}

func NewOrderService(repo repository.OrderRepository, voucherClient VoucherClient, logger *logging.Logger) OrderService {
	return &orderService{
		repo:          repo,
		voucherClient: voucherClient,
		logger:        logger,
	}
}

func (s *orderService) CreateOrder(ctx context.Context, req *model.CreateOrderRequest) (*model.Order, error) {
	log := s.logger.WithContext(ctx)
	log.Infof("Creating order for user %d with amount %.2f", req.UserID, req.OrderAmount)

	// Calculate discount internally
	calculation, err := s.calculateOrderDiscount(ctx, req.UserID, req.OrderAmount, req.OrderTimestamp, req.VoucherCode, req.Items)
	if err != nil {
		return nil, app_errors.NewInternalError(fmt.Sprintf("failed to calculate discount: %v", err))
	}

	// Create order
	order := model.NewOrder(
		req.UserID,
		req.OrderAmount,
		calculation.AppliedVoucherID,
		calculation.DiscountAmount,
		calculation.Status,
		calculation.Message,
	)

	// Create order with items in transaction
	if err := s.repo.CreateWithItems(ctx, order, req.Items); err != nil {
		return nil, app_errors.NewInternalError(fmt.Sprintf("failed to create order: %v", err))
	}

	// Increment voucher usage if voucher was applied successfully
	if calculation.AppliedVoucherID != nil && calculation.Status == "SUCCESS" {
		if err := s.voucherClient.IncrementVoucherUsage(ctx, *calculation.AppliedVoucherID, req.UserID, req.OrderAmount, req.OrderTimestamp); err != nil {
			log.Errorf("Failed to increment voucher usage for voucher %d: %v", *calculation.AppliedVoucherID, err)
			// Note: We don't fail the order creation if voucher increment fails
			// This could be handled with eventual consistency or retry mechanisms
		}
	}

	log.Infof("Order created successfully with ID %d", order.ID)
	return order, nil
}

func (s *orderService) GetOrder(ctx context.Context, orderID uint64) (*model.Order, error) {
	order, err := s.repo.GetByID(ctx, orderID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, app_errors.NewNotFoundError(fmt.Sprintf("order with id %d not found", orderID))
		}
		return nil, app_errors.NewInternalError(fmt.Sprintf("failed to get order: %v", err))
	}
	return order, nil
}

func (s *orderService) ListOrders(ctx context.Context, req *model.ListOrdersRequest) (*model.OrderWithPagination, error) {
	result, err := s.repo.List(ctx, req)
	if err != nil {
		return nil, app_errors.NewInternalError(fmt.Sprintf("failed to list orders: %v", err))
	}
	return result, nil
}

func (s *orderService) ListOrdersByVoucher(ctx context.Context, req *model.ListOrdersByVoucherRequest) (*model.OrderWithPagination, error) {
	result, err := s.repo.ListByVoucher(ctx, req)
	if err != nil {
		return nil, app_errors.NewInternalError(fmt.Sprintf("failed to list orders by voucher: %v", err))
	}
	return result, nil
}

func (s *orderService) calculateOrderDiscount(ctx context.Context, userID uint64, orderAmount float64, orderTimestamp time.Time, voucherCode *string, items []model.OrderItem) (*model.OrderCalculationResponse, error) {
	log := s.logger.WithContext(ctx)

	response := &model.OrderCalculationResponse{
		OrderAmount:    orderAmount,
		VoucherCode:    voucherCode,
		DiscountAmount: 0,
		FinalAmount:    orderAmount,
		Status:         "SUCCESS",
		Message:        "No voucher applied",
	}

	var selectedVoucher *VoucherEligibilityResponse
	var err error

	if voucherCode != nil && *voucherCode != "" {
		// Manual voucher application
		selectedVoucher, err = s.voucherClient.CheckVoucherEligibility(
			ctx, userID, *voucherCode, orderAmount, orderTimestamp, items)
		if err != nil {
			log.Errorf("Failed to check voucher eligibility: %v", err)
			response.Status = "FAILED"
			response.Message = fmt.Sprintf("Failed to validate voucher: %v", err)
			return response, nil
		}

		if !selectedVoucher.Eligible {
			response.Status = "FAILED"
			response.Message = selectedVoucher.Message
			return response, nil
		}
	} else {
		// Automatic voucher application
		eligibleVouchers, err := s.voucherClient.GetEligibleAutoVouchers(
			ctx, userID, orderAmount, orderTimestamp, items)
		if err != nil {
			log.Warnf("Failed to get eligible auto vouchers: %v", err)
		} else if len(eligibleVouchers) > 0 {
			// Select the voucher with the highest discount
			var bestDiscount float64
			for _, voucher := range eligibleVouchers {
				if voucher.DiscountAmount > bestDiscount {
					bestDiscount = voucher.DiscountAmount
					selectedVoucher = voucher
				}
			}
		}
	}

	if selectedVoucher != nil && selectedVoucher.Eligible {
		response.AppliedVoucherID = selectedVoucher.VoucherID
		response.AppliedVoucherCode = selectedVoucher.VoucherCode
		response.DiscountAmount = selectedVoucher.DiscountAmount
		response.FinalAmount = orderAmount - selectedVoucher.DiscountAmount
		response.Message = fmt.Sprintf("Voucher '%s' applied successfully", *selectedVoucher.VoucherCode)

		if response.FinalAmount < 0 {
			response.FinalAmount = 0
		}
	}

	return response, nil
}

func (s *orderService) GetUserOrderCount(ctx context.Context, userID uint64) (int64, error) {
	count, err := s.repo.GetUserOrderCount(ctx, userID)
	if err != nil {
		return 0, app_errors.NewInternalError(fmt.Sprintf("failed to get user order count: %v", err))
	}
	return count, nil
}

func (s *orderService) GetUserVoucherUsageCount(ctx context.Context, userID, voucherID uint64) (int32, error) {
	count, err := s.repo.GetUserVoucherUsageCount(ctx, userID, voucherID)
	if err != nil {
		return 0, app_errors.NewInternalError(fmt.Sprintf("failed to get user voucher usage count: %v", err))
	}
	return count, nil
}

func (s *orderService) UpdateOrderStatus(ctx context.Context, req *model.UpdateOrderStatusRequest) (*model.Order, error) {
	log := s.logger.WithContext(ctx)
	log.Infof("Updating order %d status to %s", req.OrderID, req.Status)

	if err := s.repo.UpdateStatus(ctx, req.OrderID, req.Status); err != nil {
		return nil, app_errors.NewInternalError(fmt.Sprintf("failed to update order status: %v", err))
	}

	// Return the updated order
	return s.GetOrder(ctx, req.OrderID)
}
