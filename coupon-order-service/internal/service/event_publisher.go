package service

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/kafka"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-order-service/internal/model"
)

// OrderEventPublisher handles publishing order-related events to Kafka
type OrderEventPublisher struct {
	eventPublisher *kafka.EventPublisher
	logger         *logging.Logger
	config         *config.Config
}

func NewOrderEventPublisher(cfg *config.Config, logger *logging.Logger) *OrderEventPublisher {
	eventPublisher := kafka.NewEventPublisher(&cfg.Kafka, logger, cfg.Service.Name, cfg.Service.Version)
	
	return &OrderEventPublisher{
		eventPublisher: eventPublisher,
		logger:         logger,
		config:         cfg,
	}
}

// Event payload structures
type OrderCreatedEvent struct {
	OrderID     uint64           `json:"order_id"`
	UserID      uint64           `json:"user_id"`
	OrderAmount float64          `json:"order_amount"`
	Items       []OrderItemEvent `json:"items"`
	Status      string           `json:"status"`
}

type OrderItemEvent struct {
	ProductID uint64  `json:"product_id"`
	Quantity  int32   `json:"quantity"`
	Price     float64 `json:"price"`
}

type VoucherAppliedEvent struct {
	OrderID        uint64  `json:"order_id"`
	UserID         uint64  `json:"user_id"`
	VoucherID      uint64  `json:"voucher_id"`
	VoucherCode    string  `json:"voucher_code"`
	OriginalAmount float64 `json:"original_amount"`
	DiscountAmount float64 `json:"discount_amount"`
	FinalAmount    float64 `json:"final_amount"`
}

type VoucherApplicationFailedEvent struct {
	OrderID       uint64  `json:"order_id"`
	UserID        uint64  `json:"user_id"`
	VoucherCode   string  `json:"voucher_code"`
	FailureReason string  `json:"failure_reason"`
	OrderAmount   float64 `json:"order_amount"`
}

type OrderStatusChangedEvent struct {
	OrderID   uint64 `json:"order_id"`
	UserID    uint64 `json:"user_id"`
	OldStatus string `json:"old_status"`
	NewStatus string `json:"new_status"`
	Reason    string `json:"reason"`
}

// PublishOrderCreated publishes an order created event
func (oep *OrderEventPublisher) PublishOrderCreated(ctx context.Context, order *model.Order) error {
	log := oep.logger.WithContext(ctx)

	// Convert order items to event format
	items := make([]OrderItemEvent, len(order.Items))
	for i, item := range order.Items {
		items[i] = OrderItemEvent{
			ProductID: item.ProductID,
			Quantity:  item.Quantity,
			Price:     item.Price,
		}
	}

	event := OrderCreatedEvent{
		OrderID:     order.ID,
		UserID:      order.UserID,
		OrderAmount: order.OrderAmount,
		Items:       items,
		Status:      order.CalculationStatus,
	}

	key := fmt.Sprintf("order:%d", order.ID)
	
	if err := oep.eventPublisher.PublishEvent(ctx, oep.config.Kafka.Topics.OrderEvents, "order.created", event, key); err != nil {
		log.Errorf("Failed to publish order created event for order %d: %v", order.ID, err)
		return err
	}

	log.Infof("Published order created event for order %d", order.ID)
	return nil
}

// PublishVoucherApplied publishes a voucher applied event
func (oep *OrderEventPublisher) PublishVoucherApplied(ctx context.Context, orderID, userID, voucherID uint64, voucherCode string, originalAmount, discountAmount, finalAmount float64) error {
	log := oep.logger.WithContext(ctx)

	event := VoucherAppliedEvent{
		OrderID:        orderID,
		UserID:         userID,
		VoucherID:      voucherID,
		VoucherCode:    voucherCode,
		OriginalAmount: originalAmount,
		DiscountAmount: discountAmount,
		FinalAmount:    finalAmount,
	}

	key := fmt.Sprintf("order:%d:voucher:%d", orderID, voucherID)
	
	if err := oep.eventPublisher.PublishEvent(ctx, oep.config.Kafka.Topics.OrderEvents, "order.voucher_applied", event, key); err != nil {
		log.Errorf("Failed to publish voucher applied event for order %d: %v", orderID, err)
		return err
	}

	log.Infof("Published voucher applied event for order %d with voucher %d", orderID, voucherID)
	return nil
}

// PublishVoucherApplicationFailed publishes a voucher application failed event
func (oep *OrderEventPublisher) PublishVoucherApplicationFailed(ctx context.Context, orderID, userID uint64, voucherCode, failureReason string, orderAmount float64) error {
	log := oep.logger.WithContext(ctx)

	event := VoucherApplicationFailedEvent{
		OrderID:       orderID,
		UserID:        userID,
		VoucherCode:   voucherCode,
		FailureReason: failureReason,
		OrderAmount:   orderAmount,
	}

	key := fmt.Sprintf("order:%d:voucher_failed", orderID)
	
	if err := oep.eventPublisher.PublishEvent(ctx, oep.config.Kafka.Topics.OrderEvents, "order.voucher_failed", event, key); err != nil {
		log.Errorf("Failed to publish voucher application failed event for order %d: %v", orderID, err)
		return err
	}

	log.Infof("Published voucher application failed event for order %d", orderID)
	return nil
}

// PublishOrderStatusChanged publishes an order status changed event
func (oep *OrderEventPublisher) PublishOrderStatusChanged(ctx context.Context, orderID, userID uint64, oldStatus, newStatus, reason string) error {
	log := oep.logger.WithContext(ctx)

	event := OrderStatusChangedEvent{
		OrderID:   orderID,
		UserID:    userID,
		OldStatus: oldStatus,
		NewStatus: newStatus,
		Reason:    reason,
	}

	key := fmt.Sprintf("order:%d", orderID)
	
	if err := oep.eventPublisher.PublishEvent(ctx, oep.config.Kafka.Topics.OrderEvents, "order.status_changed", event, key); err != nil {
		log.Errorf("Failed to publish order status changed event for order %d: %v", orderID, err)
		return err
	}

	log.Infof("Published order status changed event for order %d: %s -> %s", orderID, oldStatus, newStatus)
	return nil
}

// PublishOrderCreatedWithVoucherDetails publishes order created event with voucher application details
func (oep *OrderEventPublisher) PublishOrderCreatedWithVoucherDetails(ctx context.Context, order *model.Order, calculation *model.OrderCalculationResponse) error {
	log := oep.logger.WithContext(ctx)

	// First publish the order created event
	if err := oep.PublishOrderCreated(ctx, order); err != nil {
		return err
	}

	// Then publish voucher-related events based on the calculation result
	if calculation.AppliedVoucherID != nil && calculation.Status == "SUCCESS" {
		// Voucher was successfully applied
		if err := oep.PublishVoucherApplied(
			ctx,
			order.ID,
			order.UserID,
			*calculation.AppliedVoucherID,
			*calculation.AppliedVoucherCode,
			calculation.OrderAmount,
			calculation.DiscountAmount,
			calculation.FinalAmount,
		); err != nil {
			log.Errorf("Failed to publish voucher applied event: %v", err)
			// Don't return error as order creation was successful
		}
	} else if calculation.VoucherCode != nil && calculation.Status == "FAILED" {
		// Voucher application failed
		if err := oep.PublishVoucherApplicationFailed(
			ctx,
			order.ID,
			order.UserID,
			*calculation.VoucherCode,
			calculation.Message,
			calculation.OrderAmount,
		); err != nil {
			log.Errorf("Failed to publish voucher application failed event: %v", err)
			// Don't return error as order creation was successful
		}
	}

	return nil
}

// Close closes the event publisher
func (oep *OrderEventPublisher) Close() error {
	return oep.eventPublisher.Close()
}

// Helper function to convert order ID to string for event keys
func orderIDToString(id uint64) string {
	return strconv.FormatUint(id, 10)
}

// Helper function to convert user ID to string for event keys
func userIDToString(id uint64) string {
	return strconv.FormatUint(id, 10)
}
