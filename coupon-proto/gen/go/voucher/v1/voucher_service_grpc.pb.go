// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: voucher/v1/voucher_service.proto

package v1

import (
	context "context"
	v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/common/v1"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	VoucherService_CreateVoucher_FullMethodName            = "/voucher.v1.VoucherService/CreateVoucher"
	VoucherService_GetVoucher_FullMethodName               = "/voucher.v1.VoucherService/GetVoucher"
	VoucherService_GetVoucherByCode_FullMethodName         = "/voucher.v1.VoucherService/GetVoucherByCode"
	VoucherService_UpdateVoucher_FullMethodName            = "/voucher.v1.VoucherService/UpdateVoucher"
	VoucherService_ListVouchers_FullMethodName             = "/voucher.v1.VoucherService/ListVouchers"
	VoucherService_CheckVoucherEligibility_FullMethodName  = "/voucher.v1.VoucherService/CheckVoucherEligibility"
	VoucherService_ListAutoEligibleVouchers_FullMethodName = "/voucher.v1.VoucherService/ListAutoEligibleVouchers"
	VoucherService_GetDiscountTypes_FullMethodName         = "/voucher.v1.VoucherService/GetDiscountTypes"
	VoucherService_HealthCheck_FullMethodName              = "/voucher.v1.VoucherService/HealthCheck"
)

// VoucherServiceClient is the client API for VoucherService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type VoucherServiceClient interface {
	CreateVoucher(ctx context.Context, in *CreateVoucherRequest, opts ...grpc.CallOption) (*CreateVoucherResponse, error)
	GetVoucher(ctx context.Context, in *GetVoucherRequest, opts ...grpc.CallOption) (*GetVoucherResponse, error)
	GetVoucherByCode(ctx context.Context, in *GetVoucherByCodeRequest, opts ...grpc.CallOption) (*GetVoucherByCodeResponse, error)
	UpdateVoucher(ctx context.Context, in *UpdateVoucherRequest, opts ...grpc.CallOption) (*UpdateVoucherResponse, error)
	ListVouchers(ctx context.Context, in *ListVouchersRequest, opts ...grpc.CallOption) (*ListVouchersResponse, error)
	CheckVoucherEligibility(ctx context.Context, in *CheckVoucherEligibilityRequest, opts ...grpc.CallOption) (*CheckVoucherEligibilityResponse, error)
	ListAutoEligibleVouchers(ctx context.Context, in *ListAutoEligibleVouchersRequest, opts ...grpc.CallOption) (*ListAutoEligibleVouchersResponse, error)
	GetDiscountTypes(ctx context.Context, in *GetDiscountTypesRequest, opts ...grpc.CallOption) (*GetDiscountTypesResponse, error)
	HealthCheck(ctx context.Context, in *v1.HealthCheckRequest, opts ...grpc.CallOption) (*v1.HealthCheckResponse, error)
}

type voucherServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewVoucherServiceClient(cc grpc.ClientConnInterface) VoucherServiceClient {
	return &voucherServiceClient{cc}
}

func (c *voucherServiceClient) CreateVoucher(ctx context.Context, in *CreateVoucherRequest, opts ...grpc.CallOption) (*CreateVoucherResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateVoucherResponse)
	err := c.cc.Invoke(ctx, VoucherService_CreateVoucher_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *voucherServiceClient) GetVoucher(ctx context.Context, in *GetVoucherRequest, opts ...grpc.CallOption) (*GetVoucherResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetVoucherResponse)
	err := c.cc.Invoke(ctx, VoucherService_GetVoucher_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *voucherServiceClient) GetVoucherByCode(ctx context.Context, in *GetVoucherByCodeRequest, opts ...grpc.CallOption) (*GetVoucherByCodeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetVoucherByCodeResponse)
	err := c.cc.Invoke(ctx, VoucherService_GetVoucherByCode_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *voucherServiceClient) UpdateVoucher(ctx context.Context, in *UpdateVoucherRequest, opts ...grpc.CallOption) (*UpdateVoucherResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateVoucherResponse)
	err := c.cc.Invoke(ctx, VoucherService_UpdateVoucher_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *voucherServiceClient) ListVouchers(ctx context.Context, in *ListVouchersRequest, opts ...grpc.CallOption) (*ListVouchersResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListVouchersResponse)
	err := c.cc.Invoke(ctx, VoucherService_ListVouchers_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *voucherServiceClient) CheckVoucherEligibility(ctx context.Context, in *CheckVoucherEligibilityRequest, opts ...grpc.CallOption) (*CheckVoucherEligibilityResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CheckVoucherEligibilityResponse)
	err := c.cc.Invoke(ctx, VoucherService_CheckVoucherEligibility_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *voucherServiceClient) ListAutoEligibleVouchers(ctx context.Context, in *ListAutoEligibleVouchersRequest, opts ...grpc.CallOption) (*ListAutoEligibleVouchersResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListAutoEligibleVouchersResponse)
	err := c.cc.Invoke(ctx, VoucherService_ListAutoEligibleVouchers_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *voucherServiceClient) GetDiscountTypes(ctx context.Context, in *GetDiscountTypesRequest, opts ...grpc.CallOption) (*GetDiscountTypesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetDiscountTypesResponse)
	err := c.cc.Invoke(ctx, VoucherService_GetDiscountTypes_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *voucherServiceClient) HealthCheck(ctx context.Context, in *v1.HealthCheckRequest, opts ...grpc.CallOption) (*v1.HealthCheckResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v1.HealthCheckResponse)
	err := c.cc.Invoke(ctx, VoucherService_HealthCheck_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// VoucherServiceServer is the server API for VoucherService service.
// All implementations should embed UnimplementedVoucherServiceServer
// for forward compatibility.
type VoucherServiceServer interface {
	CreateVoucher(context.Context, *CreateVoucherRequest) (*CreateVoucherResponse, error)
	GetVoucher(context.Context, *GetVoucherRequest) (*GetVoucherResponse, error)
	GetVoucherByCode(context.Context, *GetVoucherByCodeRequest) (*GetVoucherByCodeResponse, error)
	UpdateVoucher(context.Context, *UpdateVoucherRequest) (*UpdateVoucherResponse, error)
	ListVouchers(context.Context, *ListVouchersRequest) (*ListVouchersResponse, error)
	CheckVoucherEligibility(context.Context, *CheckVoucherEligibilityRequest) (*CheckVoucherEligibilityResponse, error)
	ListAutoEligibleVouchers(context.Context, *ListAutoEligibleVouchersRequest) (*ListAutoEligibleVouchersResponse, error)
	GetDiscountTypes(context.Context, *GetDiscountTypesRequest) (*GetDiscountTypesResponse, error)
	HealthCheck(context.Context, *v1.HealthCheckRequest) (*v1.HealthCheckResponse, error)
}

// UnimplementedVoucherServiceServer should be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedVoucherServiceServer struct{}

func (UnimplementedVoucherServiceServer) CreateVoucher(context.Context, *CreateVoucherRequest) (*CreateVoucherResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateVoucher not implemented")
}
func (UnimplementedVoucherServiceServer) GetVoucher(context.Context, *GetVoucherRequest) (*GetVoucherResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVoucher not implemented")
}
func (UnimplementedVoucherServiceServer) GetVoucherByCode(context.Context, *GetVoucherByCodeRequest) (*GetVoucherByCodeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVoucherByCode not implemented")
}
func (UnimplementedVoucherServiceServer) UpdateVoucher(context.Context, *UpdateVoucherRequest) (*UpdateVoucherResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateVoucher not implemented")
}
func (UnimplementedVoucherServiceServer) ListVouchers(context.Context, *ListVouchersRequest) (*ListVouchersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListVouchers not implemented")
}
func (UnimplementedVoucherServiceServer) CheckVoucherEligibility(context.Context, *CheckVoucherEligibilityRequest) (*CheckVoucherEligibilityResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckVoucherEligibility not implemented")
}
func (UnimplementedVoucherServiceServer) ListAutoEligibleVouchers(context.Context, *ListAutoEligibleVouchersRequest) (*ListAutoEligibleVouchersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAutoEligibleVouchers not implemented")
}
func (UnimplementedVoucherServiceServer) GetDiscountTypes(context.Context, *GetDiscountTypesRequest) (*GetDiscountTypesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDiscountTypes not implemented")
}
func (UnimplementedVoucherServiceServer) HealthCheck(context.Context, *v1.HealthCheckRequest) (*v1.HealthCheckResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HealthCheck not implemented")
}
func (UnimplementedVoucherServiceServer) testEmbeddedByValue() {}

// UnsafeVoucherServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to VoucherServiceServer will
// result in compilation errors.
type UnsafeVoucherServiceServer interface {
	mustEmbedUnimplementedVoucherServiceServer()
}

func RegisterVoucherServiceServer(s grpc.ServiceRegistrar, srv VoucherServiceServer) {
	// If the following call pancis, it indicates UnimplementedVoucherServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&VoucherService_ServiceDesc, srv)
}

func _VoucherService_CreateVoucher_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateVoucherRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VoucherServiceServer).CreateVoucher(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VoucherService_CreateVoucher_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VoucherServiceServer).CreateVoucher(ctx, req.(*CreateVoucherRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VoucherService_GetVoucher_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVoucherRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VoucherServiceServer).GetVoucher(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VoucherService_GetVoucher_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VoucherServiceServer).GetVoucher(ctx, req.(*GetVoucherRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VoucherService_GetVoucherByCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVoucherByCodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VoucherServiceServer).GetVoucherByCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VoucherService_GetVoucherByCode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VoucherServiceServer).GetVoucherByCode(ctx, req.(*GetVoucherByCodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VoucherService_UpdateVoucher_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateVoucherRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VoucherServiceServer).UpdateVoucher(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VoucherService_UpdateVoucher_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VoucherServiceServer).UpdateVoucher(ctx, req.(*UpdateVoucherRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VoucherService_ListVouchers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListVouchersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VoucherServiceServer).ListVouchers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VoucherService_ListVouchers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VoucherServiceServer).ListVouchers(ctx, req.(*ListVouchersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VoucherService_CheckVoucherEligibility_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckVoucherEligibilityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VoucherServiceServer).CheckVoucherEligibility(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VoucherService_CheckVoucherEligibility_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VoucherServiceServer).CheckVoucherEligibility(ctx, req.(*CheckVoucherEligibilityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VoucherService_ListAutoEligibleVouchers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAutoEligibleVouchersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VoucherServiceServer).ListAutoEligibleVouchers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VoucherService_ListAutoEligibleVouchers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VoucherServiceServer).ListAutoEligibleVouchers(ctx, req.(*ListAutoEligibleVouchersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VoucherService_GetDiscountTypes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDiscountTypesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VoucherServiceServer).GetDiscountTypes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VoucherService_GetDiscountTypes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VoucherServiceServer).GetDiscountTypes(ctx, req.(*GetDiscountTypesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VoucherService_HealthCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v1.HealthCheckRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VoucherServiceServer).HealthCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VoucherService_HealthCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VoucherServiceServer).HealthCheck(ctx, req.(*v1.HealthCheckRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// VoucherService_ServiceDesc is the grpc.ServiceDesc for VoucherService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var VoucherService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "voucher.v1.VoucherService",
	HandlerType: (*VoucherServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateVoucher",
			Handler:    _VoucherService_CreateVoucher_Handler,
		},
		{
			MethodName: "GetVoucher",
			Handler:    _VoucherService_GetVoucher_Handler,
		},
		{
			MethodName: "GetVoucherByCode",
			Handler:    _VoucherService_GetVoucherByCode_Handler,
		},
		{
			MethodName: "UpdateVoucher",
			Handler:    _VoucherService_UpdateVoucher_Handler,
		},
		{
			MethodName: "ListVouchers",
			Handler:    _VoucherService_ListVouchers_Handler,
		},
		{
			MethodName: "CheckVoucherEligibility",
			Handler:    _VoucherService_CheckVoucherEligibility_Handler,
		},
		{
			MethodName: "ListAutoEligibleVouchers",
			Handler:    _VoucherService_ListAutoEligibleVouchers_Handler,
		},
		{
			MethodName: "GetDiscountTypes",
			Handler:    _VoucherService_GetDiscountTypes_Handler,
		},
		{
			MethodName: "HealthCheck",
			Handler:    _VoucherService_HealthCheck_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "voucher/v1/voucher_service.proto",
}
