// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: notification/v1/notification_service.proto

package v1

import (
	v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/common/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Notification struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	UserId        string                 `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Title         string                 `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	Message       string                 `protobuf:"bytes,4,opt,name=message,proto3" json:"message,omitempty"`
	Status        string                 `protobuf:"bytes,5,opt,name=status,proto3" json:"status,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	ReadAt        *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=read_at,json=readAt,proto3" json:"read_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Notification) Reset() {
	*x = Notification{}
	mi := &file_notification_v1_notification_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Notification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Notification) ProtoMessage() {}

func (x *Notification) ProtoReflect() protoreflect.Message {
	mi := &file_notification_v1_notification_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Notification.ProtoReflect.Descriptor instead.
func (*Notification) Descriptor() ([]byte, []int) {
	return file_notification_v1_notification_service_proto_rawDescGZIP(), []int{0}
}

func (x *Notification) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Notification) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *Notification) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Notification) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *Notification) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *Notification) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Notification) GetReadAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ReadAt
	}
	return nil
}

type SendNotificationRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.RequestMetadata    `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	UserId        string                 `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Title         string                 `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	Message       string                 `protobuf:"bytes,4,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendNotificationRequest) Reset() {
	*x = SendNotificationRequest{}
	mi := &file_notification_v1_notification_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendNotificationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendNotificationRequest) ProtoMessage() {}

func (x *SendNotificationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_notification_v1_notification_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendNotificationRequest.ProtoReflect.Descriptor instead.
func (*SendNotificationRequest) Descriptor() ([]byte, []int) {
	return file_notification_v1_notification_service_proto_rawDescGZIP(), []int{1}
}

func (x *SendNotificationRequest) GetMetadata() *v1.RequestMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *SendNotificationRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *SendNotificationRequest) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *SendNotificationRequest) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type SendNotificationResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.ResponseMetadata   `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Notification  *Notification          `protobuf:"bytes,2,opt,name=notification,proto3" json:"notification,omitempty"`
	Error         *v1.ServiceError       `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendNotificationResponse) Reset() {
	*x = SendNotificationResponse{}
	mi := &file_notification_v1_notification_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendNotificationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendNotificationResponse) ProtoMessage() {}

func (x *SendNotificationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_notification_v1_notification_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendNotificationResponse.ProtoReflect.Descriptor instead.
func (*SendNotificationResponse) Descriptor() ([]byte, []int) {
	return file_notification_v1_notification_service_proto_rawDescGZIP(), []int{2}
}

func (x *SendNotificationResponse) GetMetadata() *v1.ResponseMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *SendNotificationResponse) GetNotification() *Notification {
	if x != nil {
		return x.Notification
	}
	return nil
}

func (x *SendNotificationResponse) GetError() *v1.ServiceError {
	if x != nil {
		return x.Error
	}
	return nil
}

type UpdateNotificationStatusRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Metadata       *v1.RequestMetadata    `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	NotificationId string                 `protobuf:"bytes,2,opt,name=notification_id,json=notificationId,proto3" json:"notification_id,omitempty"`
	Status         string                 `protobuf:"bytes,3,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *UpdateNotificationStatusRequest) Reset() {
	*x = UpdateNotificationStatusRequest{}
	mi := &file_notification_v1_notification_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateNotificationStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateNotificationStatusRequest) ProtoMessage() {}

func (x *UpdateNotificationStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_notification_v1_notification_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateNotificationStatusRequest.ProtoReflect.Descriptor instead.
func (*UpdateNotificationStatusRequest) Descriptor() ([]byte, []int) {
	return file_notification_v1_notification_service_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateNotificationStatusRequest) GetMetadata() *v1.RequestMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *UpdateNotificationStatusRequest) GetNotificationId() string {
	if x != nil {
		return x.NotificationId
	}
	return ""
}

func (x *UpdateNotificationStatusRequest) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

type UpdateNotificationStatusResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.ResponseMetadata   `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Notification  *Notification          `protobuf:"bytes,2,opt,name=notification,proto3" json:"notification,omitempty"`
	Error         *v1.ServiceError       `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateNotificationStatusResponse) Reset() {
	*x = UpdateNotificationStatusResponse{}
	mi := &file_notification_v1_notification_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateNotificationStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateNotificationStatusResponse) ProtoMessage() {}

func (x *UpdateNotificationStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_notification_v1_notification_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateNotificationStatusResponse.ProtoReflect.Descriptor instead.
func (*UpdateNotificationStatusResponse) Descriptor() ([]byte, []int) {
	return file_notification_v1_notification_service_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateNotificationStatusResponse) GetMetadata() *v1.ResponseMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *UpdateNotificationStatusResponse) GetNotification() *Notification {
	if x != nil {
		return x.Notification
	}
	return nil
}

func (x *UpdateNotificationStatusResponse) GetError() *v1.ServiceError {
	if x != nil {
		return x.Error
	}
	return nil
}

type ListNotificationsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.RequestMetadata    `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	UserId        string                 `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Pagination    *v1.PaginationRequest  `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListNotificationsRequest) Reset() {
	*x = ListNotificationsRequest{}
	mi := &file_notification_v1_notification_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListNotificationsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListNotificationsRequest) ProtoMessage() {}

func (x *ListNotificationsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_notification_v1_notification_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListNotificationsRequest.ProtoReflect.Descriptor instead.
func (*ListNotificationsRequest) Descriptor() ([]byte, []int) {
	return file_notification_v1_notification_service_proto_rawDescGZIP(), []int{5}
}

func (x *ListNotificationsRequest) GetMetadata() *v1.RequestMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *ListNotificationsRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *ListNotificationsRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type ListNotificationsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.ResponseMetadata   `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Notifications []*Notification        `protobuf:"bytes,2,rep,name=notifications,proto3" json:"notifications,omitempty"`
	Pagination    *v1.PaginationResponse `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
	Error         *v1.ServiceError       `protobuf:"bytes,4,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListNotificationsResponse) Reset() {
	*x = ListNotificationsResponse{}
	mi := &file_notification_v1_notification_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListNotificationsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListNotificationsResponse) ProtoMessage() {}

func (x *ListNotificationsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_notification_v1_notification_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListNotificationsResponse.ProtoReflect.Descriptor instead.
func (*ListNotificationsResponse) Descriptor() ([]byte, []int) {
	return file_notification_v1_notification_service_proto_rawDescGZIP(), []int{6}
}

func (x *ListNotificationsResponse) GetMetadata() *v1.ResponseMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *ListNotificationsResponse) GetNotifications() []*Notification {
	if x != nil {
		return x.Notifications
	}
	return nil
}

func (x *ListNotificationsResponse) GetPagination() *v1.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListNotificationsResponse) GetError() *v1.ServiceError {
	if x != nil {
		return x.Error
	}
	return nil
}

var File_notification_v1_notification_service_proto protoreflect.FileDescriptor

const file_notification_v1_notification_service_proto_rawDesc = "" +
	"\n" +
	"*notification/v1/notification_service.proto\x12\x0fnotification.v1\x1a\x16common/v1/common.proto\x1a\x15common/v1/error.proto\x1a\x1acommon/v1/pagination.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\xef\x01\n" +
	"\fNotification\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\tR\x06userId\x12\x14\n" +
	"\x05title\x18\x03 \x01(\tR\x05title\x12\x18\n" +
	"\amessage\x18\x04 \x01(\tR\amessage\x12\x16\n" +
	"\x06status\x18\x05 \x01(\tR\x06status\x129\n" +
	"\n" +
	"created_at\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x123\n" +
	"\aread_at\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\x06readAt\"\x9a\x01\n" +
	"\x17SendNotificationRequest\x126\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1a.common.v1.RequestMetadataR\bmetadata\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\tR\x06userId\x12\x14\n" +
	"\x05title\x18\x03 \x01(\tR\x05title\x12\x18\n" +
	"\amessage\x18\x04 \x01(\tR\amessage\"\xc5\x01\n" +
	"\x18SendNotificationResponse\x127\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1b.common.v1.ResponseMetadataR\bmetadata\x12A\n" +
	"\fnotification\x18\x02 \x01(\v2\x1d.notification.v1.NotificationR\fnotification\x12-\n" +
	"\x05error\x18\x03 \x01(\v2\x17.common.v1.ServiceErrorR\x05error\"\x9a\x01\n" +
	"\x1fUpdateNotificationStatusRequest\x126\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1a.common.v1.RequestMetadataR\bmetadata\x12'\n" +
	"\x0fnotification_id\x18\x02 \x01(\tR\x0enotificationId\x12\x16\n" +
	"\x06status\x18\x03 \x01(\tR\x06status\"\xcd\x01\n" +
	" UpdateNotificationStatusResponse\x127\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1b.common.v1.ResponseMetadataR\bmetadata\x12A\n" +
	"\fnotification\x18\x02 \x01(\v2\x1d.notification.v1.NotificationR\fnotification\x12-\n" +
	"\x05error\x18\x03 \x01(\v2\x17.common.v1.ServiceErrorR\x05error\"\xa9\x01\n" +
	"\x18ListNotificationsRequest\x126\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1a.common.v1.RequestMetadataR\bmetadata\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\tR\x06userId\x12<\n" +
	"\n" +
	"pagination\x18\x03 \x01(\v2\x1c.common.v1.PaginationRequestR\n" +
	"pagination\"\x87\x02\n" +
	"\x19ListNotificationsResponse\x127\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1b.common.v1.ResponseMetadataR\bmetadata\x12C\n" +
	"\rnotifications\x18\x02 \x03(\v2\x1d.notification.v1.NotificationR\rnotifications\x12=\n" +
	"\n" +
	"pagination\x18\x03 \x01(\v2\x1d.common.v1.PaginationResponseR\n" +
	"pagination\x12-\n" +
	"\x05error\x18\x04 \x01(\v2\x17.common.v1.ServiceErrorR\x05error2\xb9\x03\n" +
	"\x13NotificationService\x12g\n" +
	"\x10SendNotification\x12(.notification.v1.SendNotificationRequest\x1a).notification.v1.SendNotificationResponse\x12\x7f\n" +
	"\x18UpdateNotificationStatus\x120.notification.v1.UpdateNotificationStatusRequest\x1a1.notification.v1.UpdateNotificationStatusResponse\x12j\n" +
	"\x11ListNotifications\x12).notification.v1.ListNotificationsRequest\x1a*.notification.v1.ListNotificationsResponse\x12L\n" +
	"\vHealthCheck\x12\x1d.common.v1.HealthCheckRequest\x1a\x1e.common.v1.HealthCheckResponseB>Z<gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/notification/v1b\x06proto3"

var (
	file_notification_v1_notification_service_proto_rawDescOnce sync.Once
	file_notification_v1_notification_service_proto_rawDescData []byte
)

func file_notification_v1_notification_service_proto_rawDescGZIP() []byte {
	file_notification_v1_notification_service_proto_rawDescOnce.Do(func() {
		file_notification_v1_notification_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_notification_v1_notification_service_proto_rawDesc), len(file_notification_v1_notification_service_proto_rawDesc)))
	})
	return file_notification_v1_notification_service_proto_rawDescData
}

var file_notification_v1_notification_service_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_notification_v1_notification_service_proto_goTypes = []any{
	(*Notification)(nil),                     // 0: notification.v1.Notification
	(*SendNotificationRequest)(nil),          // 1: notification.v1.SendNotificationRequest
	(*SendNotificationResponse)(nil),         // 2: notification.v1.SendNotificationResponse
	(*UpdateNotificationStatusRequest)(nil),  // 3: notification.v1.UpdateNotificationStatusRequest
	(*UpdateNotificationStatusResponse)(nil), // 4: notification.v1.UpdateNotificationStatusResponse
	(*ListNotificationsRequest)(nil),         // 5: notification.v1.ListNotificationsRequest
	(*ListNotificationsResponse)(nil),        // 6: notification.v1.ListNotificationsResponse
	(*timestamppb.Timestamp)(nil),            // 7: google.protobuf.Timestamp
	(*v1.RequestMetadata)(nil),               // 8: common.v1.RequestMetadata
	(*v1.ResponseMetadata)(nil),              // 9: common.v1.ResponseMetadata
	(*v1.ServiceError)(nil),                  // 10: common.v1.ServiceError
	(*v1.PaginationRequest)(nil),             // 11: common.v1.PaginationRequest
	(*v1.PaginationResponse)(nil),            // 12: common.v1.PaginationResponse
	(*v1.HealthCheckRequest)(nil),            // 13: common.v1.HealthCheckRequest
	(*v1.HealthCheckResponse)(nil),           // 14: common.v1.HealthCheckResponse
}
var file_notification_v1_notification_service_proto_depIdxs = []int32{
	7,  // 0: notification.v1.Notification.created_at:type_name -> google.protobuf.Timestamp
	7,  // 1: notification.v1.Notification.read_at:type_name -> google.protobuf.Timestamp
	8,  // 2: notification.v1.SendNotificationRequest.metadata:type_name -> common.v1.RequestMetadata
	9,  // 3: notification.v1.SendNotificationResponse.metadata:type_name -> common.v1.ResponseMetadata
	0,  // 4: notification.v1.SendNotificationResponse.notification:type_name -> notification.v1.Notification
	10, // 5: notification.v1.SendNotificationResponse.error:type_name -> common.v1.ServiceError
	8,  // 6: notification.v1.UpdateNotificationStatusRequest.metadata:type_name -> common.v1.RequestMetadata
	9,  // 7: notification.v1.UpdateNotificationStatusResponse.metadata:type_name -> common.v1.ResponseMetadata
	0,  // 8: notification.v1.UpdateNotificationStatusResponse.notification:type_name -> notification.v1.Notification
	10, // 9: notification.v1.UpdateNotificationStatusResponse.error:type_name -> common.v1.ServiceError
	8,  // 10: notification.v1.ListNotificationsRequest.metadata:type_name -> common.v1.RequestMetadata
	11, // 11: notification.v1.ListNotificationsRequest.pagination:type_name -> common.v1.PaginationRequest
	9,  // 12: notification.v1.ListNotificationsResponse.metadata:type_name -> common.v1.ResponseMetadata
	0,  // 13: notification.v1.ListNotificationsResponse.notifications:type_name -> notification.v1.Notification
	12, // 14: notification.v1.ListNotificationsResponse.pagination:type_name -> common.v1.PaginationResponse
	10, // 15: notification.v1.ListNotificationsResponse.error:type_name -> common.v1.ServiceError
	1,  // 16: notification.v1.NotificationService.SendNotification:input_type -> notification.v1.SendNotificationRequest
	3,  // 17: notification.v1.NotificationService.UpdateNotificationStatus:input_type -> notification.v1.UpdateNotificationStatusRequest
	5,  // 18: notification.v1.NotificationService.ListNotifications:input_type -> notification.v1.ListNotificationsRequest
	13, // 19: notification.v1.NotificationService.HealthCheck:input_type -> common.v1.HealthCheckRequest
	2,  // 20: notification.v1.NotificationService.SendNotification:output_type -> notification.v1.SendNotificationResponse
	4,  // 21: notification.v1.NotificationService.UpdateNotificationStatus:output_type -> notification.v1.UpdateNotificationStatusResponse
	6,  // 22: notification.v1.NotificationService.ListNotifications:output_type -> notification.v1.ListNotificationsResponse
	14, // 23: notification.v1.NotificationService.HealthCheck:output_type -> common.v1.HealthCheckResponse
	20, // [20:24] is the sub-list for method output_type
	16, // [16:20] is the sub-list for method input_type
	16, // [16:16] is the sub-list for extension type_name
	16, // [16:16] is the sub-list for extension extendee
	0,  // [0:16] is the sub-list for field type_name
}

func init() { file_notification_v1_notification_service_proto_init() }
func file_notification_v1_notification_service_proto_init() {
	if File_notification_v1_notification_service_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_notification_v1_notification_service_proto_rawDesc), len(file_notification_v1_notification_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_notification_v1_notification_service_proto_goTypes,
		DependencyIndexes: file_notification_v1_notification_service_proto_depIdxs,
		MessageInfos:      file_notification_v1_notification_service_proto_msgTypes,
	}.Build()
	File_notification_v1_notification_service_proto = out.File
	file_notification_v1_notification_service_proto_goTypes = nil
	file_notification_v1_notification_service_proto_depIdxs = nil
}
