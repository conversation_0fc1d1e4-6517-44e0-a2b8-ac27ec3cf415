syntax = "proto3";

package notification.v1;

import "common/v1/common.proto";
import "common/v1/error.proto";
import "common/v1/pagination.proto";
import "google/protobuf/timestamp.proto";

option go_package = "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/notification/v1";

service NotificationService {
  // Internal notification APIs used for service-to-service communication
  rpc SendNotification(SendNotificationRequest) returns (SendNotificationResponse);
  rpc UpdateNotificationStatus(UpdateNotificationStatusRequest) returns (UpdateNotificationStatusResponse);
  rpc ListNotifications(ListNotificationsRequest) returns (ListNotificationsResponse);

  // Health check
  rpc HealthCheck(common.v1.HealthCheckRequest) returns (common.v1.HealthCheckResponse);
}

message Notification {
  string id = 1;
  string user_id = 2;
  string title = 3;
  string message = 4;
  string status = 5;
  google.protobuf.Timestamp created_at = 6;
  google.protobuf.Timestamp read_at = 7;
}

message SendNotificationRequest {
  common.v1.RequestMetadata metadata = 1;
  string user_id = 2;
  string title = 3;
  string message = 4;
}

message SendNotificationResponse {
  common.v1.ResponseMetadata metadata = 1;
  Notification notification = 2;
  common.v1.ServiceError error = 3;
}

message UpdateNotificationStatusRequest {
  common.v1.RequestMetadata metadata = 1;
  string notification_id = 2;
  string status = 3;
}

message UpdateNotificationStatusResponse {
  common.v1.ResponseMetadata metadata = 1;
  Notification notification = 2;
  common.v1.ServiceError error = 3;
}

message ListNotificationsRequest {
  common.v1.RequestMetadata metadata = 1;
  string user_id = 2;
  common.v1.PaginationRequest pagination = 3;
}

message ListNotificationsResponse {
  common.v1.ResponseMetadata metadata = 1;
  repeated Notification notifications = 2;
  common.v1.PaginationResponse pagination = 3;
  common.v1.ServiceError error = 4;
}
