syntax = "proto3";

package notification.v1;

import "common/v1/common.proto";
import "common/v1/error.proto";
import "common/v1/pagination.proto";
import "google/protobuf/timestamp.proto";

option go_package = "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/notification/v1";

service NotificationService {
  // Internal notification APIs used for service-to-service communication
  rpc SendNotification(SendNotificationRequest) returns (SendNotificationResponse);
  rpc UpdateNotificationStatus(UpdateNotificationStatusRequest) returns (UpdateNotificationStatusResponse);
  rpc ListNotifications(ListNotificationsRequest) returns (ListNotificationsResponse);

  // Template-based notification creation
  rpc CreateNotificationFromTemplate(CreateNotificationFromTemplateRequest) returns (CreateNotificationFromTemplateResponse);

  // User preference management
  rpc GetUserPreferences(GetUserPreferencesRequest) returns (GetUserPreferencesResponse);
  rpc UpdateUserPreferences(UpdateUserPreferencesRequest) returns (UpdateUserPreferencesResponse);

  // Health check
  rpc HealthCheck(common.v1.HealthCheckRequest) returns (common.v1.HealthCheckResponse);
}

// Enums
enum NotificationStatus {
  NOTIFICATION_STATUS_UNSPECIFIED = 0;
  NOTIFICATION_STATUS_PENDING = 1;
  NOTIFICATION_STATUS_SENT = 2;
  NOTIFICATION_STATUS_FAILED = 3;
  NOTIFICATION_STATUS_READ = 4;
  NOTIFICATION_STATUS_CANCELLED = 5;
}

enum NotificationChannel {
  NOTIFICATION_CHANNEL_UNSPECIFIED = 0;
  NOTIFICATION_CHANNEL_EMAIL = 1;
  NOTIFICATION_CHANNEL_IN_APP = 2;
}

enum NotificationType {
  NOTIFICATION_TYPE_UNSPECIFIED = 0;
  NOTIFICATION_TYPE_VOUCHER_CREATED = 1;
  NOTIFICATION_TYPE_VOUCHER_EXPIRING = 2;
  NOTIFICATION_TYPE_VOUCHER_USED = 3;
  NOTIFICATION_TYPE_ORDER_CONFIRMATION = 4;
  NOTIFICATION_TYPE_VOUCHER_APPLIED = 5;
  NOTIFICATION_TYPE_VOUCHER_FAILED = 6;
  NOTIFICATION_TYPE_USER_WELCOME = 7;
  NOTIFICATION_TYPE_USER_TYPE_UPGRADE = 8;
}

message Notification {
  uint64 id = 1;
  uint64 user_id = 2;
  NotificationType type = 3;
  string title = 4;
  string message = 5;
  NotificationStatus status = 6;
  NotificationChannel channel = 7;
  string metadata = 8; // JSON string for additional data
  google.protobuf.Timestamp scheduled_at = 9;
  google.protobuf.Timestamp sent_at = 10;
  google.protobuf.Timestamp read_at = 11;
  google.protobuf.Timestamp created_at = 12;
  google.protobuf.Timestamp updated_at = 13;
}

message SendNotificationRequest {
  common.v1.RequestMetadata metadata = 1;
  NotificationType type = 2;
  string title = 3;
  string message = 4;
  NotificationChannel channel = 5; // Optional, will use user preference if not specified
  google.protobuf.Timestamp scheduled_at = 6; // Optional, for scheduled notifications
}

message SendNotificationResponse {
  common.v1.ResponseMetadata metadata = 1;
  Notification notification = 2;
  common.v1.ServiceError error = 3;
}

message UpdateNotificationStatusRequest {
  common.v1.RequestMetadata metadata = 1;
  uint64 notification_id = 2;
  NotificationStatus status = 3;
}

message UpdateNotificationStatusResponse {
  common.v1.ResponseMetadata metadata = 1;
  Notification notification = 2;
  common.v1.ServiceError error = 3;
}

message ListNotificationsRequest {
  common.v1.RequestMetadata metadata = 1;
  common.v1.PaginationRequest pagination = 2;
  // Filters
  repeated NotificationStatus status_filter = 3;
  repeated NotificationType type_filter = 4;
  repeated NotificationChannel channel_filter = 5;
  google.protobuf.Timestamp from_date = 6;
  google.protobuf.Timestamp to_date = 7;
}

message ListNotificationsResponse {
  common.v1.ResponseMetadata metadata = 1;
  repeated Notification notifications = 2;
  common.v1.PaginationResponse pagination = 3;
  common.v1.ServiceError error = 4;
}

// Template-based notification messages
message CreateNotificationFromTemplateRequest {
  common.v1.RequestMetadata metadata = 1;
  string template_key = 2;
  string template_variables = 3; // JSON string with template variables
  google.protobuf.Timestamp scheduled_at = 4; // Optional
}

message CreateNotificationFromTemplateResponse {
  common.v1.ResponseMetadata metadata = 1;
  Notification notification = 2;
  common.v1.ServiceError error = 3;
}

// User preference messages
message UserNotificationPreference {
  uint64 id = 1;
  uint64 user_id = 2;
  NotificationType type = 3;
  NotificationChannel preferred_channel = 4;
  bool is_enabled = 5;
  string channel_settings = 6; // JSON string for channel-specific settings
  google.protobuf.Timestamp created_at = 7;
  google.protobuf.Timestamp updated_at = 8;
}

message GetUserPreferencesRequest {
  common.v1.RequestMetadata metadata = 1;
}

message GetUserPreferencesResponse {
  common.v1.ResponseMetadata metadata = 1;
  repeated UserNotificationPreference preferences = 2;
  common.v1.ServiceError error = 3;
}

message UpdateUserPreferencesRequest {
  common.v1.RequestMetadata metadata = 1;
  repeated UserNotificationPreference preferences = 2;
}

message UpdateUserPreferencesResponse {
  common.v1.ResponseMetadata metadata = 1;
  repeated UserNotificationPreference preferences = 2;
  common.v1.ServiceError error = 3;
}
