# Notification Service Makefile

.PHONY: help build run test clean docker-build docker-up docker-down docker-logs proto-gen

# Default target
help:
	@echo "Available targets:"
	@echo "  build         - Build the notification service binary"
	@echo "  run           - Run the notification service locally"
	@echo "  test          - Run tests"
	@echo "  clean         - Clean build artifacts"
	@echo "  docker-build  - Build Docker image"
	@echo "  docker-up     - Start service with Docker Compose"
	@echo "  docker-down   - Stop service with Docker Compose"
	@echo "  docker-logs   - Show Docker logs"
	@echo "  proto-gen     - Generate protobuf files"

# Build the binary
build:
	go build -o bin/notification-service ./cmd/server

# Run the service locally
run:
	go run ./cmd/server

# Run tests
test:
	go test -v ./...

# Clean build artifacts
clean:
	rm -rf bin/
	go clean

# Docker targets
docker-build:
	docker build -t notification-service .

docker-up:
	docker-compose up -d

docker-down:
	docker-compose down

docker-logs:
	docker-compose logs -f

# Generate protobuf files (if needed)
proto-gen:
	@echo "Protobuf files should be generated from the coupon-proto directory"

# Development helpers
dev-setup:
	go mod tidy
	go mod download

lint:
	golangci-lint run

format:
	go fmt ./...

# Database migration (if needed)
migrate-up:
	@echo "Database migrations are handled automatically by the service"

migrate-down:
	@echo "Manual migration rollback not implemented"
