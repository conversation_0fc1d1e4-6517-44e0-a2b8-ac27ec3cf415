version: '3.8'

services:
  notification-service:
    build: .
    container_name: notification-service
    restart: unless-stopped
    ports:
      - "8085:8080"
      - "50055:50051"
    environment:
      - NOTIFICATION_SERVICE_CLIENT_ID=notification-service-client
      - NOTIFICATION_SERVICE_CLIENT_KEY=notification-service-secret-key
      - POSTGRES_USER=notification_user
      - POSTGRES_PASSWORD=notification_password
      - POSTGRES_DB=notification_db
      - REDIS_PASSWORD=notification_redis_password
      - SMTP_HOST=smtp.gmail.com
      - SMTP_USERNAME=${SMTP_USERNAME}
      - SMTP_PASSWORD=${SMTP_PASSWORD}
      - SMTP_FROM_ADDRESS=${SMTP_FROM_ADDRESS}
      - FCM_SERVER_KEY=${FCM_SERVER_KEY}
      - FCM_PROJECT_ID=${FCM_PROJECT_ID}
      - TWILIO_ACCOUNT_SID=${TWILIO_ACCOUNT_SID}
      - TWILIO_AUTH_TOKEN=${TWILIO_AUTH_TOKEN}
      - TWILIO_FROM_NUMBER=${TWILIO_FROM_NUMBER}
    depends_on:
      postgres-notification:
        condition: service_healthy
      redis-notification:
        condition: service_healthy
    networks:
      - coupon-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  postgres-notification:
    image: postgres:15-alpine
    container_name: postgres-notification
    restart: unless-stopped
    environment:
      - POSTGRES_USER=notification_user
      - POSTGRES_PASSWORD=notification_password
      - POSTGRES_DB=notification_db
    ports:
      - "5437:5432"
    volumes:
      - postgres-notification-data:/var/lib/postgresql/data
    networks:
      - coupon-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U notification_user -d notification_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis-notification:
    image: redis:7-alpine
    container_name: redis-notification
    restart: unless-stopped
    command: redis-server --requirepass notification_redis_password
    ports:
      - "6384:6379"
    volumes:
      - redis-notification-data:/data
    networks:
      - coupon-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  postgres-notification-data:
  redis-notification-data:

networks:
  coupon-network:
    name: coupon-network
    external: true
