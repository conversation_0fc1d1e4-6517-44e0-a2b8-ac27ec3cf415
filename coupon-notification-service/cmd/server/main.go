package main

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	"github.com/labstack/echo/v4"
	proto_notification_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/notification/v1"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/database"
	shared_grpc "gitlab.zalopay.vn/phunn4/coupon-shared-libs/grpc"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/health"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/metrics"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/redis"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/tracing"
	"gitlab.zalopay.vn/phunn4/coupon-notification-service/internal/clients"
	"gitlab.zalopay.vn/phunn4/coupon-notification-service/internal/consumers"
	grpc_handler "gitlab.zalopay.vn/phunn4/coupon-notification-service/internal/handler/grpc"
	"gitlab.zalopay.vn/phunn4/coupon-notification-service/internal/middleware"
	"gitlab.zalopay.vn/phunn4/coupon-notification-service/internal/model"
	"gitlab.zalopay.vn/phunn4/coupon-notification-service/internal/repository"
	"gitlab.zalopay.vn/phunn4/coupon-notification-service/internal/service"
)

func main() {
	cfg, err := config.Load()
	if err != nil {
		panic(fmt.Sprintf("config error: %v", err))
	}

	logger := logging.New(cfg.Logging.Level, cfg.Logging.Format)
	logger.Infof("Starting service: %s v%s", cfg.Service.Name, cfg.Service.Version)

	tracer, err := tracing.New(cfg.Service.Name, cfg.Jaeger.Host, cfg.Jaeger.Port)
	if err != nil {
		logger.Fatalf("tracer error: %v", err)
	}

	appMetrics := metrics.New(cfg.Service.Name)

	db, err := database.NewPostgresDB(&cfg.Database, logger, appMetrics)
	if err != nil {
		logger.Fatalf("db error: %v", err)
	}

	autoMigrator := database.NewAutoMigrator(db, logger)
	if err := autoMigrator.AutoMigrate(model.GetAllModels()...); err != nil {
		logger.Fatalf("failed to run database migrations: %v", err)
	}

	redisClient := redis.NewClient(&cfg.Redis, logger, appMetrics)

	// Initialize clients
	authClient, err := clients.NewAuthClient(cfg.DownstreamServices.AuthServiceAddr, &cfg.GRPC, logger, appMetrics, cfg.Service.ClientID, cfg.Service.ClientKey)
	if err != nil {
		logger.Fatalf("auth client error: %v", err)
	}

	userClient, err := clients.NewUserClient(cfg.DownstreamServices.UserServiceAddr, &cfg.GRPC, logger, appMetrics, cfg.Service.ClientID, cfg.Service.ClientKey)
	if err != nil {
		logger.Fatalf("user client error: %v", err)
	}

	// Initialize repository and services
	repo := repository.NewNotificationRepository(db)
	templateEngine := service.NewTemplateEngine(repo, redisClient, logger)
	deliveryService := service.NewDeliveryService(repo, logger, cfg)
	
	// Register delivery channels
	deliveryService.RegisterChannel(model.ChannelInApp, service.NewInAppChannelHandler(logger))
	deliveryService.RegisterChannel(model.ChannelEmail, service.NewEmailChannelHandler(logger, cfg))
	deliveryService.RegisterChannel(model.ChannelPush, service.NewPushChannelHandler(logger, cfg))
	deliveryService.RegisterChannel(model.ChannelSMS, service.NewSMSChannelHandler(logger, cfg))

	notificationSvc := service.NewNotificationService(repo, templateEngine, deliveryService, logger, cfg)

	// Load templates into cache
	if err := templateEngine.LoadTemplates(context.Background()); err != nil {
		logger.Fatalf("failed to load templates: %v", err)
	}

	// Seed default templates if needed
	if err := seedDefaultTemplates(context.Background(), repo, logger); err != nil {
		logger.Fatalf("failed to seed default templates: %v", err)
	}

	// Initialize event consumers
	consumerManager := consumers.NewConsumerManager(cfg, notificationSvc, templateEngine, logger)

	healthChecker := health.NewHealthChecker()
	healthChecker.AddCheck("database", db.Health)
	healthChecker.AddCheck("redis", redisClient.Health)

	ctx, stop := signal.NotifyContext(context.Background(), os.Interrupt, syscall.SIGTERM)
	defer stop()

	var wg sync.WaitGroup
	wg.Add(3)

	// Start gRPC server
	go func() {
		defer wg.Done()
		authFunc := middleware.CreateServiceAuthFunc(authClient)
		startGRPCServer(ctx, cfg, logger, appMetrics, notificationSvc, authFunc)
	}()

	// Start HTTP server
	go func() {
		defer wg.Done()
		startHTTPServer(ctx, cfg, healthChecker, appMetrics, logger)
	}()

	// Start event consumers
	go func() {
		defer wg.Done()
		startEventConsumers(ctx, consumerManager, logger)
	}()

	// Start background processors
	go func() {
		startBackgroundProcessors(ctx, notificationSvc, deliveryService, logger)
	}()

	wg.Wait()
	tracer.Close()
	db.Close()
	redisClient.Close()
	authClient.Close()
	userClient.Close()
	consumerManager.Stop()
	logger.Info("Shutdown complete")
}

func startGRPCServer(ctx context.Context, cfg *config.Config, logger *logging.Logger, metrics *metrics.Metrics, svc service.NotificationService, authFunc func(context.Context) (context.Context, error)) {
	grpcServerWrapper := shared_grpc.NewServer(&cfg.GRPC, logger, metrics, authFunc)
	grpcHandler := grpc_handler.NewNotificationServer(svc)
	proto_notification_v1.RegisterNotificationServiceServer(grpcServerWrapper.Server, grpcHandler)

	go func() {
		if err := grpcServerWrapper.Start(); err != nil {
			logger.Errorf("gRPC server failed: %v", err)
		}
	}()

	<-ctx.Done()
	grpcServerWrapper.Stop()
}

func startHTTPServer(ctx context.Context, cfg *config.Config, healthChecker *health.HealthChecker, metrics *metrics.Metrics, logger *logging.Logger) {
	e := echo.New()
	e.GET("/health", healthChecker.HTTPHandler())
	e.GET(cfg.Metrics.Path, echo.WrapHandler(metrics.Handler()))

	addr := fmt.Sprintf(":%d", cfg.Service.Port)
	server := &http.Server{Addr: addr, Handler: e}

	logger.Infof("Starting operational HTTP server on %s", addr)
	go func() {
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Fatalf("HTTP server failed: %v", err)
		}
	}()

	<-ctx.Done()
	shutdownCtx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := server.Shutdown(shutdownCtx); err != nil {
		logger.Fatalf("HTTP server shutdown failed: %v", err)
	}
}

func startEventConsumers(ctx context.Context, consumerManager *consumers.ConsumerManager, logger *logging.Logger) {
	logger.Info("Starting event consumers")
	if err := consumerManager.Start(ctx); err != nil {
		logger.Errorf("Failed to start event consumers: %v", err)
	}

	<-ctx.Done()
	logger.Info("Stopping event consumers")
	consumerManager.Stop()
}

func startBackgroundProcessors(ctx context.Context, notificationSvc service.NotificationService, deliveryService service.DeliveryService, logger *logging.Logger) {
	logger.Info("Starting background processors")

	// Process scheduled notifications every minute
	scheduledTicker := time.NewTicker(1 * time.Minute)
	defer scheduledTicker.Stop()

	// Retry failed deliveries every 5 minutes
	retryTicker := time.NewTicker(5 * time.Minute)
	defer retryTicker.Stop()

	for {
		select {
		case <-scheduledTicker.C:
			if err := notificationSvc.ProcessScheduledNotifications(ctx); err != nil {
				logger.Errorf("Failed to process scheduled notifications: %v", err)
			}
		case <-retryTicker.C:
			if err := deliveryService.RetryFailedDeliveries(ctx); err != nil {
				logger.Errorf("Failed to retry failed deliveries: %v", err)
			}
		case <-ctx.Done():
			logger.Info("Stopping background processors")
			return
		}
	}
}

func seedDefaultTemplates(ctx context.Context, repo repository.NotificationRepository, logger *logging.Logger) error {
	logger.Info("Seeding default notification templates")

	templates := service.GetDefaultTemplates()
	for _, template := range templates {
		// Check if template already exists
		existing, err := repo.GetTemplateByKey(ctx, template.TemplateKey)
		if err == nil && existing != nil {
			logger.Debugf("Template %s already exists, skipping", template.TemplateKey)
			continue
		}

		// Create the template
		if err := repo.CreateTemplate(ctx, template); err != nil {
			logger.Errorf("Failed to create template %s: %v", template.TemplateKey, err)
			return err
		}

		logger.Infof("Created template: %s", template.TemplateKey)
	}

	logger.Info("Default templates seeded successfully")
	return nil
}
