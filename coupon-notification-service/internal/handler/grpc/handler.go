package grpc

import (
	"context"

	"gitlab.zalopay.vn/phunn4/coupon-notification-service/internal/service"
	proto_common_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/common/v1"
	proto_notification_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/notification/v1"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/errors"
)

type NotificationServer struct {
	proto_notification_v1.UnimplementedNotificationServiceServer
	svc service.NotificationService
}

func NewNotificationServer(svc service.NotificationService) *NotificationServer {
	return &NotificationServer{svc: svc}
}

func (s *NotificationServer) SendNotification(ctx context.Context, req *proto_notification_v1.SendNotificationRequest) (*proto_notification_v1.SendNotificationResponse, error) {
	return s.svc.SendNotification(ctx, req)
}

func (s *NotificationServer) UpdateNotificationStatus(ctx context.Context, req *proto_notification_v1.UpdateNotificationStatusRequest) (*proto_notification_v1.UpdateNotificationStatusResponse, error) {
	return s.svc.UpdateNotificationStatus(ctx, req)
}

func (s *NotificationServer) ListNotifications(ctx context.Context, req *proto_notification_v1.ListNotificationsRequest) (*proto_notification_v1.ListNotificationsResponse, error) {
	return s.svc.ListNotifications(ctx, req)
}

func (s *NotificationServer) HealthCheck(ctx context.Context, req *proto_common_v1.HealthCheckRequest) (*proto_common_v1.HealthCheckResponse, error) {
	return &proto_common_v1.HealthCheckResponse{
		Status: proto_common_v1.HealthStatus_HEALTH_STATUS_SERVING,
	}, nil
}
