package clients

import (
	"context"
	"fmt"

	proto_auth_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/auth/v1"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	shared_grpc "gitlab.zalopay.vn/phunn4/coupon-shared-libs/grpc"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/metrics"
	"google.golang.org/grpc"
)

type AuthClient struct {
	client   proto_auth_v1.AuthServiceClient
	conn     *grpc.ClientConn
	logger   *logging.Logger
	clientID string
	clientKey string
}

func NewAuthClient(addr string, grpcConfig *config.GRPCConfig, logger *logging.Logger, metrics *metrics.Metrics, clientID, clientKey string) (*AuthClient, error) {
	conn, err := shared_grpc.NewClientConnection(addr, grpcConfig, logger, metrics)
	if err != nil {
		return nil, fmt.Errorf("failed to create gRPC connection: %w", err)
	}

	client := proto_auth_v1.NewAuthServiceClient(conn)

	return &AuthClient{
		client:   client,
		conn:     conn,
		logger:   logger,
		clientID: clientID,
		clientKey: clientKey,
	}, nil
}

func (c *AuthClient) ValidateServiceCredentials(ctx context.Context, clientID, clientKey string) error {
	req := &proto_auth_v1.ValidateServiceCredentialsRequest{
		ClientId:  clientID,
		ClientKey: clientKey,
	}

	_, err := c.client.ValidateServiceCredentials(ctx, req)
	return err
}

func (c *AuthClient) Close() error {
	if c.conn != nil {
		return c.conn.Close()
	}
	return nil
}
