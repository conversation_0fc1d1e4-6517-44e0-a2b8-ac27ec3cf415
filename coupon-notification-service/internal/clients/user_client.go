package clients

import (
	"context"
	"fmt"

	proto_user_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/user/v1"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	shared_grpc "gitlab.zalopay.vn/phunn4/coupon-shared-libs/grpc"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/metrics"
	"google.golang.org/grpc"
)

type UserClient struct {
	client   proto_user_v1.UserServiceClient
	conn     *grpc.ClientConn
	logger   *logging.Logger
	clientID string
	clientKey string
}

func NewUserClient(addr string, grpcConfig *config.GRPCConfig, logger *logging.Logger, metrics *metrics.Metrics, clientID, clientKey string) (*UserClient, error) {
	conn, err := shared_grpc.NewClientConnection(addr, grpcConfig, logger, metrics)
	if err != nil {
		return nil, fmt.Errorf("failed to create gRPC connection: %w", err)
	}

	client := proto_user_v1.NewUserServiceClient(conn)

	return &UserClient{
		client:   client,
		conn:     conn,
		logger:   logger,
		clientID: clientID,
		clientKey: clientKey,
	}, nil
}

func (c *UserClient) GetUser(ctx context.Context, userID uint64) (*proto_user_v1.User, error) {
	req := &proto_user_v1.GetUserRequest{
		UserId: userID,
	}

	resp, err := c.client.GetUser(ctx, req)
	if err != nil {
		return nil, err
	}

	return resp.User, nil
}

func (c *UserClient) GetUserByEmail(ctx context.Context, email string) (*proto_user_v1.User, error) {
	req := &proto_user_v1.GetUserByEmailRequest{
		Email: email,
	}

	resp, err := c.client.GetUserByEmail(ctx, req)
	if err != nil {
		return nil, err
	}

	return resp.User, nil
}

func (c *UserClient) Close() error {
	if c.conn != nil {
		return c.conn.Close()
	}
	return nil
}
