package consumers

import (
	"context"
	"encoding/json"
	"fmt"

	"gitlab.zalopay.vn/phunn4/coupon-notification-service/internal/model"
	"gitlab.zalopay.vn/phunn4/coupon-notification-service/internal/service"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/kafka"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
)

type OrderEventConsumer struct {
	consumer           *kafka.Consumer
	notificationSvc    service.NotificationService
	templateEngine     service.TemplateEngine
	logger             *logging.Logger
	config             *config.Config
}

func NewOrderEventConsumer(
	cfg *config.Config,
	notificationSvc service.NotificationService,
	templateEngine service.TemplateEngine,
	logger *logging.Logger,
) *OrderEventConsumer {
	consumer := kafka.NewConsumer(&cfg.Kafka, logger)
	
	return &OrderEventConsumer{
		consumer:        consumer,
		notificationSvc: notificationSvc,
		templateEngine:  templateEngine,
		logger:          logger,
		config:          cfg,
	}
}

func (oec *OrderEventConsumer) Start(ctx context.Context) error {
	log := oec.logger.WithContext(ctx)
	log.Info("Starting order event consumer")

	return oec.consumer.Subscribe(ctx, oec.config.Kafka.Topics.OrderEvents, oec.handleMessage)
}

func (oec *OrderEventConsumer) Stop() error {
	oec.logger.Info("Stopping order event consumer")
	return oec.consumer.Close()
}

func (oec *OrderEventConsumer) handleMessage(ctx context.Context, message *kafka.Message) error {
	log := oec.logger.WithContext(ctx)

	// Parse the event wrapper
	var eventWrapper map[string]interface{}
	if err := json.Unmarshal(message.Value.([]byte), &eventWrapper); err != nil {
		log.Errorf("Failed to unmarshal event wrapper: %v", err)
		return err
	}

	eventType, ok := eventWrapper["event_type"].(string)
	if !ok {
		log.Error("Event type not found in message")
		return fmt.Errorf("event type not found")
	}

	payload, ok := eventWrapper["payload"]
	if !ok {
		log.Error("Payload not found in message")
		return fmt.Errorf("payload not found")
	}

	log.Infof("Processing order event: %s", eventType)

	switch eventType {
	case "order.created":
		return oec.handleOrderCreated(ctx, payload)
	case "order.voucher_applied":
		return oec.handleVoucherApplied(ctx, payload)
	case "order.voucher_failed":
		return oec.handleVoucherApplicationFailed(ctx, payload)
	case "order.status_changed":
		return oec.handleOrderStatusChanged(ctx, payload)
	default:
		log.Warnf("Unknown order event type: %s", eventType)
		return nil
	}
}

func (oec *OrderEventConsumer) handleOrderCreated(ctx context.Context, payload interface{}) error {
	log := oec.logger.WithContext(ctx)

	// Parse the payload
	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		return err
	}

	var event model.OrderCreatedPayload
	if err := json.Unmarshal(payloadBytes, &event); err != nil {
		log.Errorf("Failed to unmarshal order created event: %v", err)
		return err
	}

	log.Infof("Processing order created event for order %d", event.OrderID)

	// Create notification from template
	variables := map[string]interface{}{
		"OrderID":     event.OrderID,
		"UserID":      event.UserID,
		"OrderAmount": event.OrderAmount,
		"Status":      event.Status,
	}

	notification, err := oec.notificationSvc.CreateNotificationFromTemplate(
		ctx,
		event.UserID,
		"order_confirmation",
		variables,
	)
	if err != nil {
		log.Errorf("Failed to create notification from template: %v", err)
		return err
	}

	log.Infof("Created order confirmation notification %d for user %d", notification.ID, event.UserID)
	return nil
}

func (oec *OrderEventConsumer) handleVoucherApplied(ctx context.Context, payload interface{}) error {
	log := oec.logger.WithContext(ctx)

	// Parse the payload
	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		return err
	}

	var event model.VoucherAppliedPayload
	if err := json.Unmarshal(payloadBytes, &event); err != nil {
		log.Errorf("Failed to unmarshal voucher applied event: %v", err)
		return err
	}

	log.Infof("Processing voucher applied event for order %d", event.OrderID)

	// Create notification from template
	variables := map[string]interface{}{
		"OrderID":        event.OrderID,
		"UserID":         event.UserID,
		"VoucherID":      event.VoucherID,
		"VoucherCode":    event.VoucherCode,
		"OriginalAmount": event.OriginalAmount,
		"DiscountAmount": event.DiscountAmount,
		"FinalAmount":    event.FinalAmount,
	}

	notification, err := oec.notificationSvc.CreateNotificationFromTemplate(
		ctx,
		event.UserID,
		"voucher_applied",
		variables,
	)
	if err != nil {
		log.Errorf("Failed to create notification from template: %v", err)
		return err
	}

	log.Infof("Created voucher applied notification %d for user %d", notification.ID, event.UserID)
	return nil
}

func (oec *OrderEventConsumer) handleVoucherApplicationFailed(ctx context.Context, payload interface{}) error {
	log := oec.logger.WithContext(ctx)

	// Parse the payload
	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		return err
	}

	var event struct {
		OrderID       uint64  `json:"order_id"`
		UserID        uint64  `json:"user_id"`
		VoucherCode   string  `json:"voucher_code"`
		FailureReason string  `json:"failure_reason"`
		OrderAmount   float64 `json:"order_amount"`
	}
	if err := json.Unmarshal(payloadBytes, &event); err != nil {
		log.Errorf("Failed to unmarshal voucher application failed event: %v", err)
		return err
	}

	log.Infof("Processing voucher application failed event for order %d", event.OrderID)

	// Create notification from template
	variables := map[string]interface{}{
		"OrderID":       event.OrderID,
		"UserID":        event.UserID,
		"VoucherCode":   event.VoucherCode,
		"FailureReason": event.FailureReason,
		"OrderAmount":   event.OrderAmount,
	}

	notification, err := oec.notificationSvc.CreateNotificationFromTemplate(
		ctx,
		event.UserID,
		"voucher_failed",
		variables,
	)
	if err != nil {
		log.Errorf("Failed to create notification from template: %v", err)
		return err
	}

	log.Infof("Created voucher failed notification %d for user %d", notification.ID, event.UserID)
	return nil
}

func (oec *OrderEventConsumer) handleOrderStatusChanged(ctx context.Context, payload interface{}) error {
	log := oec.logger.WithContext(ctx)

	// Parse the payload
	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		return err
	}

	var event struct {
		OrderID   uint64 `json:"order_id"`
		UserID    uint64 `json:"user_id"`
		OldStatus string `json:"old_status"`
		NewStatus string `json:"new_status"`
		Reason    string `json:"reason"`
	}
	if err := json.Unmarshal(payloadBytes, &event); err != nil {
		log.Errorf("Failed to unmarshal order status changed event: %v", err)
		return err
	}

	log.Infof("Processing order status changed event for order %d: %s -> %s", 
		event.OrderID, event.OldStatus, event.NewStatus)

	// For order status changes, we might want to notify users about important status changes
	// like "shipped", "delivered", "cancelled", etc.
	// For now, we'll just log the event

	log.Infof("Order status changed event processed for order %d", event.OrderID)
	return nil
}
