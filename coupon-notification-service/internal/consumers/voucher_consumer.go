package consumers

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"gitlab.zalopay.vn/phunn4/coupon-notification-service/internal/model"
	"gitlab.zalopay.vn/phunn4/coupon-notification-service/internal/service"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/kafka"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
)

type VoucherEventConsumer struct {
	consumer           *kafka.Consumer
	notificationSvc    service.NotificationService
	templateEngine     service.TemplateEngine
	logger             *logging.Logger
	config             *config.Config
}

func NewVoucherEventConsumer(
	cfg *config.Config,
	notificationSvc service.NotificationService,
	templateEngine service.TemplateEngine,
	logger *logging.Logger,
) *VoucherEventConsumer {
	consumer := kafka.NewConsumer(&cfg.Kafka, logger)
	
	return &VoucherEventConsumer{
		consumer:        consumer,
		notificationSvc: notificationSvc,
		templateEngine:  templateEngine,
		logger:          logger,
		config:          cfg,
	}
}

func (vec *VoucherEventConsumer) Start(ctx context.Context) error {
	log := vec.logger.WithContext(ctx)
	log.Info("Starting voucher event consumer")

	return vec.consumer.Subscribe(ctx, vec.config.Kafka.Topics.VoucherEvents, vec.handleMessage)
}

func (vec *VoucherEventConsumer) Stop() error {
	vec.logger.Info("Stopping voucher event consumer")
	return vec.consumer.Close()
}

func (vec *VoucherEventConsumer) handleMessage(ctx context.Context, message *kafka.Message) error {
	log := vec.logger.WithContext(ctx)

	// Parse the event wrapper
	var eventWrapper map[string]interface{}
	if err := json.Unmarshal(message.Value.([]byte), &eventWrapper); err != nil {
		log.Errorf("Failed to unmarshal event wrapper: %v", err)
		return err
	}

	eventType, ok := eventWrapper["event_type"].(string)
	if !ok {
		log.Error("Event type not found in message")
		return fmt.Errorf("event type not found")
	}

	payload, ok := eventWrapper["payload"]
	if !ok {
		log.Error("Payload not found in message")
		return fmt.Errorf("payload not found")
	}

	log.Infof("Processing voucher event: %s", eventType)

	switch eventType {
	case "voucher.created":
		return vec.handleVoucherCreated(ctx, payload)
	case "voucher.used":
		return vec.handleVoucherUsed(ctx, payload)
	case "voucher.expiring":
		return vec.handleVoucherExpiring(ctx, payload)
	case "voucher.status_changed":
		return vec.handleVoucherStatusChanged(ctx, payload)
	default:
		log.Warnf("Unknown voucher event type: %s", eventType)
		return nil
	}
}

func (vec *VoucherEventConsumer) handleVoucherCreated(ctx context.Context, payload interface{}) error {
	log := vec.logger.WithContext(ctx)

	// Parse the payload
	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		return err
	}

	var event model.VoucherCreatedPayload
	if err := json.Unmarshal(payloadBytes, &event); err != nil {
		log.Errorf("Failed to unmarshal voucher created event: %v", err)
		return err
	}

	log.Infof("Processing voucher created event for voucher %d", event.VoucherID)

	// Create notification from template
	variables := map[string]interface{}{
		"VoucherID":            event.VoucherID,
		"VoucherCode":          event.VoucherCode,
		"Title":                event.Title,
		"DiscountValue":        event.DiscountValue,
		"ValidFrom":            event.ValidFrom,
		"ValidUntil":           event.ValidUntil,
		"UserEligibilityType":  event.UserEligibilityType,
	}

	// For voucher created events, we might want to notify specific users based on eligibility
	// For now, we'll skip this as it requires user eligibility logic
	// In a real implementation, you would query eligible users and create notifications for them

	log.Infof("Voucher created event processed for voucher %d", event.VoucherID)
	return nil
}

func (vec *VoucherEventConsumer) handleVoucherUsed(ctx context.Context, payload interface{}) error {
	log := vec.logger.WithContext(ctx)

	// Parse the payload
	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		return err
	}

	var event model.VoucherUsedPayload
	if err := json.Unmarshal(payloadBytes, &event); err != nil {
		log.Errorf("Failed to unmarshal voucher used event: %v", err)
		return err
	}

	log.Infof("Processing voucher used event for voucher %d by user %d", event.VoucherID, event.UserID)

	// Create notification from template
	variables := map[string]interface{}{
		"VoucherID":      event.VoucherID,
		"VoucherCode":    event.VoucherCode,
		"UserID":         event.UserID,
		"OrderID":        event.OrderID,
		"DiscountAmount": event.DiscountAmount,
		"OrderAmount":    event.OrderAmount,
	}

	notification, err := vec.notificationSvc.CreateNotificationFromTemplate(
		ctx,
		event.UserID,
		"voucher_used",
		variables,
	)
	if err != nil {
		log.Errorf("Failed to create notification from template: %v", err)
		return err
	}

	log.Infof("Created voucher used notification %d for user %d", notification.ID, event.UserID)
	return nil
}

func (vec *VoucherEventConsumer) handleVoucherExpiring(ctx context.Context, payload interface{}) error {
	log := vec.logger.WithContext(ctx)

	// Parse the payload
	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		return err
	}

	var event model.VoucherExpiringPayload
	if err := json.Unmarshal(payloadBytes, &event); err != nil {
		log.Errorf("Failed to unmarshal voucher expiring event: %v", err)
		return err
	}

	log.Infof("Processing voucher expiring event for voucher %d", event.VoucherID)

	// Create notification from template for each eligible user
	variables := map[string]interface{}{
		"VoucherID":         event.VoucherID,
		"VoucherCode":       event.VoucherCode,
		"ExpiresAt":         event.ExpiresAt,
		"HoursUntilExpiry":  event.HoursUntilExpiry,
	}

	// Create notifications for all eligible users
	for _, userID := range event.EligibleUserIDs {
		notification, err := vec.notificationSvc.CreateNotificationFromTemplate(
			ctx,
			userID,
			"voucher_expiring",
			variables,
		)
		if err != nil {
			log.Errorf("Failed to create expiring notification for user %d: %v", userID, err)
			continue
		}

		log.Infof("Created voucher expiring notification %d for user %d", notification.ID, userID)
	}

	return nil
}

func (vec *VoucherEventConsumer) handleVoucherStatusChanged(ctx context.Context, payload interface{}) error {
	log := vec.logger.WithContext(ctx)

	// Parse the payload
	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		return err
	}

	var event struct {
		VoucherID   uint64 `json:"voucher_id"`
		VoucherCode string `json:"voucher_code"`
		OldStatus   string `json:"old_status"`
		NewStatus   string `json:"new_status"`
		Reason      string `json:"reason"`
	}
	if err := json.Unmarshal(payloadBytes, &event); err != nil {
		log.Errorf("Failed to unmarshal voucher status changed event: %v", err)
		return err
	}

	log.Infof("Processing voucher status changed event for voucher %d: %s -> %s", 
		event.VoucherID, event.OldStatus, event.NewStatus)

	// For status changes, we might want to notify users if a voucher becomes inactive
	// This would require additional business logic to determine which users to notify
	// For now, we'll just log the event

	log.Infof("Voucher status changed event processed for voucher %d", event.VoucherID)
	return nil
}
