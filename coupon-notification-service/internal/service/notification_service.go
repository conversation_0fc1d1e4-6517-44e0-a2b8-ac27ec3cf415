package service

import (
	"context"
	"fmt"
	"time"

	"gitlab.zalopay.vn/phunn4/coupon-notification-service/internal/model"
	"gitlab.zalopay.vn/phunn4/coupon-notification-service/internal/repository"
	proto_common_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/common/v1"
	proto_notification_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/notification/v1"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/errors"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type NotificationService interface {
	// gRPC service methods
	SendNotification(ctx context.Context, req *proto_notification_v1.SendNotificationRequest) (*proto_notification_v1.SendNotificationResponse, error)
	UpdateNotificationStatus(ctx context.Context, req *proto_notification_v1.UpdateNotificationStatusRequest) (*proto_notification_v1.UpdateNotificationStatusResponse, error)
	ListNotifications(ctx context.Context, req *proto_notification_v1.ListNotificationsRequest) (*proto_notification_v1.ListNotificationsResponse, error)

	// Internal service methods
	ProcessNotification(ctx context.Context, notification *model.Notification) error
	ProcessScheduledNotifications(ctx context.Context) error
	CreateNotificationFromTemplate(ctx context.Context, userID uint64, templateKey string, variables map[string]interface{}) (*model.Notification, error)
	GetUserPreferredChannel(ctx context.Context, userID uint64, notificationType model.NotificationType) (model.NotificationChannel, error)
}

type notificationService struct {
	repo            repository.NotificationRepository
	templateEngine  TemplateEngine
	deliveryService DeliveryService
	logger          *logging.Logger
	config          *config.Config
}

func NewNotificationService(
	repo repository.NotificationRepository,
	templateEngine TemplateEngine,
	deliveryService DeliveryService,
	logger *logging.Logger,
	cfg *config.Config,
) NotificationService {
	return &notificationService{
		repo:            repo,
		templateEngine:  templateEngine,
		deliveryService: deliveryService,
		logger:          logger,
		config:          cfg,
	}
}

// gRPC service implementations
func (s *notificationService) SendNotification(ctx context.Context, req *proto_notification_v1.SendNotificationRequest) (*proto_notification_v1.SendNotificationResponse, error) {
	log := s.logger.WithContext(ctx)

	// Parse user ID from metadata
	userID := req.Metadata.UserId
	if userID == 0 {
		return nil, errors.NewValidationError("user_id is required", nil)
	}

	// Convert proto enums to model types
	notificationType := s.convertProtoToModelNotificationType(req.Type)
	channel := s.convertProtoToModelChannel(req.Channel)

	// If no channel specified, use user preference or default
	if channel == "" {
		preferredChannel, err := s.GetUserPreferredChannel(ctx, userID, notificationType)
		if err != nil {
			log.Warnf("Failed to get user preferred channel, using default: %v", err)
			channel = model.ChannelInApp // Default fallback
		} else {
			channel = preferredChannel
		}
	}

	// Create notification
	var notification *model.Notification
	if req.ScheduledAt != nil {
		scheduledAt := req.ScheduledAt.AsTime()
		notification = model.NewScheduledNotification(
			userID,
			notificationType,
			req.Title,
			req.Message,
			channel,
			scheduledAt,
		)
	} else {
		notification = model.NewNotification(
			userID,
			notificationType,
			req.Title,
			req.Message,
			channel,
		)
	}

	// Save to database
	if err := s.repo.CreateNotification(ctx, notification); err != nil {
		log.Errorf("Failed to create notification: %v", err)
		return nil, errors.NewInternalError("failed to create notification")
	}

	// Process notification asynchronously
	go func() {
		if err := s.ProcessNotification(context.Background(), notification); err != nil {
			log.Errorf("Failed to process notification %d: %v", notification.ID, err)
		}
	}()

	// Convert to proto response
	protoNotification := s.convertToProtoNotification(notification)

	return &proto_notification_v1.SendNotificationResponse{
		Metadata: &proto_common_v1.ResponseMetadata{
			RequestId:        req.Metadata.RequestId,
			Timestamp:        timestamppb.Now(),
			ServiceName:      s.config.Service.Name,
			ServiceVersion:   s.config.Service.Version,
			ProcessingTimeMs: 0, // Should be calculated
		},
		Notification: protoNotification,
	}, nil
}

func (s *notificationService) UpdateNotificationStatus(ctx context.Context, req *proto_notification_v1.UpdateNotificationStatusRequest) (*proto_notification_v1.UpdateNotificationStatusResponse, error) {
	log := s.logger.WithContext(ctx)

	// Parse notification ID
	notificationID := req.NotificationId
	if notificationID == 0 {
		return nil, errors.NewValidationError("invalid notification_id", nil)
	}

	// Convert proto status to model status
	status := s.convertProtoToModelStatus(req.Status)
	if err := s.repo.UpdateNotificationStatus(ctx, notificationID, status); err != nil {
		log.Errorf("Failed to update notification status: %v", err)
		return nil, errors.NewInternalError("failed to update notification status")
	}

	// Get updated notification
	notification, err := s.repo.GetNotificationByID(ctx, notificationID)
	if err != nil {
		log.Errorf("Failed to get updated notification: %v", err)
		return nil, errors.NewInternalError("failed to get updated notification")
	}

	return &proto_notification_v1.UpdateNotificationStatusResponse{
		Metadata: &proto_common_v1.ResponseMetadata{
			RequestId:        req.Metadata.RequestId,
			Timestamp:        timestamppb.Now(),
			ServiceName:      s.config.Service.Name,
			ServiceVersion:   s.config.Service.Version,
			ProcessingTimeMs: 0,
		},
		Notification: s.convertToProtoNotification(notification),
	}, nil
}

func (s *notificationService) ListNotifications(ctx context.Context, req *proto_notification_v1.ListNotificationsRequest) (*proto_notification_v1.ListNotificationsResponse, error) {
	log := s.logger.WithContext(ctx)

	userID := req.Metadata.UserId
	if userID == 0 {
		return nil, errors.NewValidationError("user_id is required", nil)
	}

	// Default pagination
	pageSize := int(req.Pagination.PageSize)
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 20
	}
	page := int(req.Pagination.Page)
	if page <= 0 {
		page = 1
	}
	offset := (page - 1) * pageSize

	// Get notifications
	notifications, total, err := s.repo.ListNotificationsByUser(ctx, userID, pageSize, offset)
	if err != nil {
		log.Errorf("Failed to list notifications: %v", err)
		return nil, errors.NewInternalError("failed to list notifications")
	}

	// Convert to proto
	protoNotifications := make([]*proto_notification_v1.Notification, len(notifications))
	for i, notification := range notifications {
		protoNotifications[i] = s.convertToProtoNotification(notification)
	}

	return &proto_notification_v1.ListNotificationsResponse{
		Metadata: &proto_common_v1.ResponseMetadata{
			RequestId:        req.Metadata.RequestId,
			Timestamp:        timestamppb.Now(),
			ServiceName:      s.config.Service.Name,
			ServiceVersion:   s.config.Service.Version,
			ProcessingTimeMs: 0,
		},
		Notifications: protoNotifications,
		Pagination: &proto_common_v1.PaginationResponse{
			CurrentPage: int32(page),
			PageSize:    int32(pageSize),
			TotalItems:  total,
			TotalPages:  int32((total + int64(pageSize) - 1) / int64(pageSize)), // Calculate total pages
			HasNext:     int64(offset+pageSize) < total,
			HasPrevious: page > 1,
		},
	}, nil
}

// Internal service methods
func (s *notificationService) ProcessNotification(ctx context.Context, notification *model.Notification) error {
	log := s.logger.WithContext(ctx).WithField("notification_id", notification.ID)

	// Check if notification should be processed now
	if notification.ScheduledAt != nil && notification.ScheduledAt.After(time.Now()) {
		log.Debug("Notification is scheduled for later, skipping")
		return nil
	}

	// Get user preferred channel if not set
	if notification.Channel == "" {
		preferredChannel, err := s.GetUserPreferredChannel(ctx, notification.UserID, notification.Type)
		if err != nil {
			log.Warnf("Failed to get user preferred channel, using default: %v", err)
			preferredChannel = model.ChannelEmail // Default fallback
		}
		notification.Channel = preferredChannel
	}

	// Deliver notification
	if err := s.deliveryService.DeliverNotification(ctx, notification); err != nil {
		log.Errorf("Failed to deliver notification: %v", err)
		// Update status to failed
		s.repo.UpdateNotificationStatus(ctx, notification.ID, model.NotificationStatusFailed)
		return err
	}

	// Update status to sent
	if err := s.repo.UpdateNotificationStatus(ctx, notification.ID, model.NotificationStatusSent); err != nil {
		log.Errorf("Failed to update notification status to sent: %v", err)
		return err
	}

	log.Info("Notification processed successfully")
	return nil
}

func (s *notificationService) ProcessScheduledNotifications(ctx context.Context) error {
	log := s.logger.WithContext(ctx)

	// Get scheduled notifications that are due
	notifications, err := s.repo.GetScheduledNotifications(ctx, time.Now(), 100)
	if err != nil {
		log.Errorf("Failed to get scheduled notifications: %v", err)
		return err
	}

	log.Infof("Processing %d scheduled notifications", len(notifications))

	for _, notification := range notifications {
		if err := s.ProcessNotification(ctx, notification); err != nil {
			log.Errorf("Failed to process scheduled notification %d: %v", notification.ID, err)
			// Continue processing other notifications
		}
	}

	return nil
}

func (s *notificationService) CreateNotificationFromTemplate(ctx context.Context, userID uint64, templateKey string, variables map[string]interface{}) (*model.Notification, error) {
	log := s.logger.WithContext(ctx)

	// Get template
	template, err := s.repo.GetTemplateByKey(ctx, templateKey)
	if err != nil {
		log.Errorf("Failed to get template %s: %v", templateKey, err)
		return nil, err
	}

	// Render template
	title, err := s.templateEngine.RenderTemplate(template.TitleTemplate, variables)
	if err != nil {
		log.Errorf("Failed to render title template: %v", err)
		return nil, err
	}

	message, err := s.templateEngine.RenderTemplate(template.MessageTemplate, variables)
	if err != nil {
		log.Errorf("Failed to render message template: %v", err)
		return nil, err
	}

	// Get user preferred channel
	preferredChannel, err := s.GetUserPreferredChannel(ctx, userID, template.Type)
	if err != nil {
		log.Warnf("Failed to get user preferred channel, using template default: %v", err)
		preferredChannel = template.DefaultChannel
	}

	// Create notification
	notification := model.NewNotification(userID, template.Type, title, message, preferredChannel)

	// Save to database
	if err := s.repo.CreateNotification(ctx, notification); err != nil {
		log.Errorf("Failed to create notification from template: %v", err)
		return nil, err
	}

	return notification, nil
}

func (s *notificationService) GetUserPreferredChannel(ctx context.Context, userID uint64, notificationType model.NotificationType) (model.NotificationChannel, error) {
	preference, err := s.repo.GetUserPreference(ctx, userID, notificationType)
	if err != nil {
		// Return default channel if no preference found
		return model.ChannelEmail, nil
	}

	if !preference.IsEnabled {
		return "", fmt.Errorf("notifications disabled for user %d and type %s", userID, notificationType)
	}

	return preference.PreferredChannel, nil
}

// Helper functions
func (s *notificationService) convertToProtoNotification(notification *model.Notification) *proto_notification_v1.Notification {
	protoNotification := &proto_notification_v1.Notification{
		Id:        notification.ID,
		UserId:    notification.UserID,
		Type:      s.convertModelToProtoNotificationType(notification.Type),
		Title:     notification.Title,
		Message:   notification.Message,
		Status:    s.convertModelToProtoStatus(notification.Status),
		Channel:   s.convertModelToProtoChannel(notification.Channel),
		CreatedAt: timestamppb.New(notification.CreatedAt),
	}

	if notification.ScheduledAt != nil {
		protoNotification.ScheduledAt = timestamppb.New(*notification.ScheduledAt)
	}

	if notification.SentAt != nil {
		protoNotification.SentAt = timestamppb.New(*notification.SentAt)
	}

	if notification.ReadAt != nil {
		protoNotification.ReadAt = timestamppb.New(*notification.ReadAt)
	}

	if !notification.UpdatedAt.IsZero() {
		protoNotification.UpdatedAt = timestamppb.New(notification.UpdatedAt)
	}

	// Convert metadata to JSON string if needed
	if notification.Metadata != nil {
		metadataBytes, _ := notification.Metadata.MarshalJSON()
		protoNotification.Metadata = string(metadataBytes)
	}

	return protoNotification
}

// Conversion functions between proto and model types
func (s *notificationService) convertModelToProtoNotificationType(modelType model.NotificationType) proto_notification_v1.NotificationType {
	switch modelType {
	case model.TypeVoucherCreated:
		return proto_notification_v1.NotificationType_NOTIFICATION_TYPE_VOUCHER_CREATED
	case model.TypeVoucherExpiring:
		return proto_notification_v1.NotificationType_NOTIFICATION_TYPE_VOUCHER_EXPIRING
	case model.TypeVoucherUsed:
		return proto_notification_v1.NotificationType_NOTIFICATION_TYPE_VOUCHER_USED
	case model.TypeOrderConfirmation:
		return proto_notification_v1.NotificationType_NOTIFICATION_TYPE_ORDER_CONFIRMATION
	case model.TypeVoucherApplied:
		return proto_notification_v1.NotificationType_NOTIFICATION_TYPE_VOUCHER_APPLIED
	case model.TypeVoucherFailed:
		return proto_notification_v1.NotificationType_NOTIFICATION_TYPE_VOUCHER_FAILED
	case model.TypeUserWelcome:
		return proto_notification_v1.NotificationType_NOTIFICATION_TYPE_USER_WELCOME
	case model.TypeUserTypeUpgrade:
		return proto_notification_v1.NotificationType_NOTIFICATION_TYPE_USER_TYPE_UPGRADE
	default:
		return proto_notification_v1.NotificationType_NOTIFICATION_TYPE_ORDER_CONFIRMATION
	}
}

func (s *notificationService) convertModelToProtoStatus(modelStatus model.NotificationStatus) proto_notification_v1.NotificationStatus {
	switch modelStatus {
	case model.NotificationStatusPending:
		return proto_notification_v1.NotificationStatus_NOTIFICATION_STATUS_PENDING
	case model.NotificationStatusSent:
		return proto_notification_v1.NotificationStatus_NOTIFICATION_STATUS_SENT
	case model.NotificationStatusFailed:
		return proto_notification_v1.NotificationStatus_NOTIFICATION_STATUS_FAILED
	case model.NotificationStatusRead:
		return proto_notification_v1.NotificationStatus_NOTIFICATION_STATUS_READ
	case model.NotificationStatusCancelled:
		return proto_notification_v1.NotificationStatus_NOTIFICATION_STATUS_CANCELLED
	default:
		return proto_notification_v1.NotificationStatus_NOTIFICATION_STATUS_PENDING
	}
}

func (s *notificationService) convertModelToProtoChannel(modelChannel model.NotificationChannel) proto_notification_v1.NotificationChannel {
	switch modelChannel {
	case model.ChannelEmail:
		return proto_notification_v1.NotificationChannel_NOTIFICATION_CHANNEL_EMAIL
	case model.ChannelInApp:
		return proto_notification_v1.NotificationChannel_NOTIFICATION_CHANNEL_IN_APP
	default:
		return proto_notification_v1.NotificationChannel_NOTIFICATION_CHANNEL_IN_APP
	}
}

// Reverse conversion functions (proto to model)
func (s *notificationService) convertProtoToModelNotificationType(protoType proto_notification_v1.NotificationType) model.NotificationType {
	switch protoType {
	case proto_notification_v1.NotificationType_NOTIFICATION_TYPE_VOUCHER_CREATED:
		return model.TypeVoucherCreated
	case proto_notification_v1.NotificationType_NOTIFICATION_TYPE_VOUCHER_EXPIRING:
		return model.TypeVoucherExpiring
	case proto_notification_v1.NotificationType_NOTIFICATION_TYPE_VOUCHER_USED:
		return model.TypeVoucherUsed
	case proto_notification_v1.NotificationType_NOTIFICATION_TYPE_ORDER_CONFIRMATION:
		return model.TypeOrderConfirmation
	case proto_notification_v1.NotificationType_NOTIFICATION_TYPE_VOUCHER_APPLIED:
		return model.TypeVoucherApplied
	case proto_notification_v1.NotificationType_NOTIFICATION_TYPE_VOUCHER_FAILED:
		return model.TypeVoucherFailed
	case proto_notification_v1.NotificationType_NOTIFICATION_TYPE_USER_WELCOME:
		return model.TypeUserWelcome
	case proto_notification_v1.NotificationType_NOTIFICATION_TYPE_USER_TYPE_UPGRADE:
		return model.TypeUserTypeUpgrade
	default:
		return model.TypeOrderConfirmation
	}
}

func (s *notificationService) convertProtoToModelStatus(protoStatus proto_notification_v1.NotificationStatus) model.NotificationStatus {
	switch protoStatus {
	case proto_notification_v1.NotificationStatus_NOTIFICATION_STATUS_PENDING:
		return model.NotificationStatusPending
	case proto_notification_v1.NotificationStatus_NOTIFICATION_STATUS_SENT:
		return model.NotificationStatusSent
	case proto_notification_v1.NotificationStatus_NOTIFICATION_STATUS_FAILED:
		return model.NotificationStatusFailed
	case proto_notification_v1.NotificationStatus_NOTIFICATION_STATUS_READ:
		return model.NotificationStatusRead
	case proto_notification_v1.NotificationStatus_NOTIFICATION_STATUS_CANCELLED:
		return model.NotificationStatusCancelled
	default:
		return model.NotificationStatusPending
	}
}

func (s *notificationService) convertProtoToModelChannel(protoChannel proto_notification_v1.NotificationChannel) model.NotificationChannel {
	switch protoChannel {
	case proto_notification_v1.NotificationChannel_NOTIFICATION_CHANNEL_EMAIL:
		return model.ChannelEmail
	case proto_notification_v1.NotificationChannel_NOTIFICATION_CHANNEL_IN_APP:
		return model.ChannelInApp
	default:
		return model.ChannelInApp // Default fallback
	}
}
