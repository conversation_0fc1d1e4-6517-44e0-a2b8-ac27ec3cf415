package service

import (
	"bytes"
	"context"
	"fmt"
	"strings"
	"text/template"
	"time"

	"gitlab.zalopay.vn/phunn4/coupon-notification-service/internal/model"
	"gitlab.zalopay.vn/phunn4/coupon-notification-service/internal/repository"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/redis"
)

type TemplateEngine interface {
	RenderTemplate(templateStr string, variables map[string]interface{}) (string, error)
	RenderNotificationFromTemplate(ctx context.Context, templateKey string, variables map[string]interface{}) (title, message string, channel model.NotificationChannel, err error)
	LoadTemplates(ctx context.Context) error
	GetTemplate(ctx context.Context, templateKey string) (*model.NotificationTemplate, error)
}

type templateEngine struct {
	repo          repository.NotificationRepository
	redisClient   *redis.Client
	logger        *logging.Logger
	templateCache map[string]*template.Template
	cacheTTL      time.Duration
}

func NewTemplateEngine(repo repository.NotificationRepository, redisClient *redis.Client, logger *logging.Logger) TemplateEngine {
	return &templateEngine{
		repo:          repo,
		redisClient:   redisClient,
		logger:        logger,
		templateCache: make(map[string]*template.Template),
		cacheTTL:      time.Hour, // 1 hour cache TTL
	}
}

func (te *templateEngine) RenderTemplate(templateStr string, variables map[string]interface{}) (string, error) {
	// Create template with helper functions
	tmpl, err := template.New("notification").Funcs(te.getTemplateFunctions()).Parse(templateStr)
	if err != nil {
		return "", fmt.Errorf("failed to parse template: %w", err)
	}

	// Render template
	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, variables); err != nil {
		return "", fmt.Errorf("failed to execute template: %w", err)
	}

	return buf.String(), nil
}

func (te *templateEngine) RenderNotificationFromTemplate(ctx context.Context, templateKey string, variables map[string]interface{}) (title, message string, channel model.NotificationChannel, err error) {
	log := te.logger.WithContext(ctx)

	// Get template from cache or database
	notificationTemplate, err := te.GetTemplate(ctx, templateKey)
	if err != nil {
		log.Errorf("Failed to get template %s: %v", templateKey, err)
		return "", "", "", err
	}

	// Render title
	title, err = te.RenderTemplate(notificationTemplate.TitleTemplate, variables)
	if err != nil {
		log.Errorf("Failed to render title template: %v", err)
		return "", "", "", err
	}

	// Render message
	message, err = te.RenderTemplate(notificationTemplate.MessageTemplate, variables)
	if err != nil {
		log.Errorf("Failed to render message template: %v", err)
		return "", "", "", err
	}

	return title, message, notificationTemplate.DefaultChannel, nil
}

func (te *templateEngine) LoadTemplates(ctx context.Context) error {
	log := te.logger.WithContext(ctx)

	templates, err := te.repo.ListActiveTemplates(ctx)
	if err != nil {
		log.Errorf("Failed to load templates: %v", err)
		return err
	}

	log.Infof("Loading %d templates into cache", len(templates))

	for _, tmpl := range templates {
		// Cache template in Redis
		cacheKey := fmt.Sprintf("notification:template:%s", tmpl.TemplateKey)
		if err := te.redisClient.SetJSON(ctx, cacheKey, tmpl, te.cacheTTL); err != nil {
			log.Warnf("Failed to cache template %s: %v", tmpl.TemplateKey, err)
		}

		// Pre-compile templates for faster rendering
		titleTmpl, err := template.New(tmpl.TemplateKey + "_title").Funcs(te.getTemplateFunctions()).Parse(tmpl.TitleTemplate)
		if err != nil {
			log.Errorf("Failed to compile title template %s: %v", tmpl.TemplateKey, err)
			continue
		}

		messageTmpl, err := template.New(tmpl.TemplateKey + "_message").Funcs(te.getTemplateFunctions()).Parse(tmpl.MessageTemplate)
		if err != nil {
			log.Errorf("Failed to compile message template %s: %v", tmpl.TemplateKey, err)
			continue
		}

		te.templateCache[tmpl.TemplateKey+"_title"] = titleTmpl
		te.templateCache[tmpl.TemplateKey+"_message"] = messageTmpl
	}

	return nil
}

func (te *templateEngine) GetTemplate(ctx context.Context, templateKey string) (*model.NotificationTemplate, error) {
	log := te.logger.WithContext(ctx)

	// Try to get from Redis cache first
	cacheKey := fmt.Sprintf("notification:template:%s", templateKey)
	var template model.NotificationTemplate

	if err := te.redisClient.GetJSON(ctx, cacheKey, &template); err == nil {
		return &template, nil
	}

	// Get from database if not in cache
	dbTemplate, err := te.repo.GetTemplateByKey(ctx, templateKey)
	if err != nil {
		return nil, err
	}

	// Cache for future use
	if err := te.redisClient.SetJSON(ctx, cacheKey, dbTemplate, te.cacheTTL); err != nil {
		log.Warnf("Failed to cache template %s: %v", templateKey, err)
	}

	return dbTemplate, nil
}

// Template helper functions
func (te *templateEngine) getTemplateFunctions() template.FuncMap {
	return template.FuncMap{
		"formatCurrency": func(amount float64) string {
			return fmt.Sprintf("$%.2f", amount)
		},
		"formatDate": func(t time.Time) string {
			return t.Format("January 2, 2006")
		},
		"formatDateTime": func(t time.Time) string {
			return t.Format("January 2, 2006 at 3:04 PM")
		},
		"upper": strings.ToUpper,
		"lower": strings.ToLower,
		"title": strings.Title,
		"pluralize": func(count int, singular, plural string) string {
			if count == 1 {
				return singular
			}
			return plural
		},
		"timeUntil": func(t time.Time) string {
			duration := time.Until(t)
			if duration < 0 {
				return "expired"
			}

			hours := int(duration.Hours())
			if hours < 24 {
				return fmt.Sprintf("%d hours", hours)
			}

			days := hours / 24
			return fmt.Sprintf("%d days", days)
		},
		"percentage": func(value float64) string {
			return fmt.Sprintf("%.0f%%", value*100)
		},
	}
}

// Default templates for seeding
func GetDefaultTemplates() []*model.NotificationTemplate {
	return []*model.NotificationTemplate{
		{
			TemplateKey:     "voucher_created",
			Type:            model.TypeVoucherCreated,
			TitleTemplate:   "New Voucher Available: {{.VoucherCode}}",
			MessageTemplate: "A new voucher '{{.Title}}' worth {{formatCurrency .DiscountValue}} is now available! Use code {{.VoucherCode}} before {{formatDate .ValidUntil}}.",
			DefaultChannel:  model.ChannelEmail,
			IsActive:        true,
		},
		{
			TemplateKey:     "voucher_expiring",
			Type:            model.TypeVoucherExpiring,
			TitleTemplate:   "Voucher Expiring Soon: {{.VoucherCode}}",
			MessageTemplate: "Your voucher {{.VoucherCode}} will expire in {{timeUntil .ExpiresAt}}. Don't miss out on your savings!",
			DefaultChannel:  model.ChannelEmail,
			IsActive:        true,
		},
		{
			TemplateKey:     "voucher_used",
			Type:            model.TypeVoucherUsed,
			TitleTemplate:   "Voucher Applied Successfully",
			MessageTemplate: "You saved {{formatCurrency .DiscountAmount}} with voucher {{.VoucherCode}} on your order #{{.OrderID}}!",
			DefaultChannel:  model.ChannelInApp,
			IsActive:        true,
		},
		{
			TemplateKey:     "order_confirmation",
			Type:            model.TypeOrderConfirmation,
			TitleTemplate:   "Order Confirmed #{{.OrderID}}",
			MessageTemplate: "Your order #{{.OrderID}} for {{formatCurrency .OrderAmount}} has been confirmed and is being processed.",
			DefaultChannel:  model.ChannelEmail,
			IsActive:        true,
		},
		{
			TemplateKey:     "voucher_applied",
			Type:            model.TypeVoucherApplied,
			TitleTemplate:   "Great Savings on Order #{{.OrderID}}",
			MessageTemplate: "Congratulations! You saved {{formatCurrency .DiscountAmount}} with voucher {{.VoucherCode}}. Your final total is {{formatCurrency .FinalAmount}}.",
			DefaultChannel:  model.ChannelInApp,
			IsActive:        true,
		},
		{
			TemplateKey:     "voucher_failed",
			Type:            model.TypeVoucherFailed,
			TitleTemplate:   "Voucher Could Not Be Applied",
			MessageTemplate: "We couldn't apply voucher {{.VoucherCode}} to your order. Reason: {{.FailureReason}}. Please try a different voucher.",
			DefaultChannel:  model.ChannelInApp,
			IsActive:        true,
		},
		{
			TemplateKey:     "user_welcome",
			Type:            model.TypeUserWelcome,
			TitleTemplate:   "Welcome to Coupon System, {{.Name}}!",
			MessageTemplate: "Welcome {{.Name}}! Thank you for joining our coupon system. Start saving money with exclusive deals and vouchers.",
			DefaultChannel:  model.ChannelEmail,
			IsActive:        true,
		},
		{
			TemplateKey:     "user_type_upgrade",
			Type:            model.TypeUserTypeUpgrade,
			TitleTemplate:   "Congratulations! You're now a {{.NewType}} member",
			MessageTemplate: "Great news {{.Name}}! You've been upgraded to {{.NewType}} status and now have access to exclusive vouchers and better deals.",
			DefaultChannel:  model.ChannelEmail,
			IsActive:        true,
		},
	}
}
