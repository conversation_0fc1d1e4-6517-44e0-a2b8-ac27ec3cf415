package service

import (
	"context"
	"fmt"
	"time"

	"gitlab.zalopay.vn/phunn4/coupon-notification-service/internal/model"
	"gitlab.zalopay.vn/phunn4/coupon-notification-service/internal/repository"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
)

type DeliveryService interface {
	DeliverNotification(ctx context.Context, notification *model.Notification) error
	RetryFailedDeliveries(ctx context.Context) error
	RegisterChannel(channel model.NotificationChannel, handler ChannelHandler)
}

type ChannelHandler interface {
	SendNotification(ctx context.Context, notification *model.Notification) error
	GetChannelType() model.NotificationChannel
	IsEnabled() bool
}

type deliveryService struct {
	repo     repository.NotificationRepository
	logger   *logging.Logger
	config   *config.Config
	channels map[model.NotificationChannel]ChannelHandler
}

func NewDeliveryService(repo repository.NotificationRepository, logger *logging.Logger, cfg *config.Config) DeliveryService {
	return &deliveryService{
		repo:     repo,
		logger:   logger,
		config:   cfg,
		channels: make(map[model.NotificationChannel]ChannelHandler),
	}
}

func (ds *deliveryService) RegisterChannel(channel model.NotificationChannel, handler ChannelHandler) {
	ds.channels[channel] = handler
	ds.logger.Infof("Registered notification channel: %s", channel)
}

func (ds *deliveryService) DeliverNotification(ctx context.Context, notification *model.Notification) error {
	log := ds.logger.WithContext(ctx).WithField("notification_id", notification.ID)

	// Create delivery log
	deliveryLog := model.NewDeliveryLog(notification.ID, notification.Channel)
	if err := ds.repo.CreateDeliveryLog(ctx, deliveryLog); err != nil {
		log.Errorf("Failed to create delivery log: %v", err)
		return err
	}

	// Get channel handler
	handler, exists := ds.channels[notification.Channel]
	if !exists {
		err := fmt.Errorf("no handler registered for channel: %s", notification.Channel)
		log.Error(err.Error())
		ds.updateDeliveryLogStatus(ctx, deliveryLog, model.DeliveryStatusFailed, err.Error())
		return err
	}

	// Check if channel is enabled
	if !handler.IsEnabled() {
		err := fmt.Errorf("channel %s is disabled", notification.Channel)
		log.Warn(err.Error())
		ds.updateDeliveryLogStatus(ctx, deliveryLog, model.DeliveryStatusFailed, err.Error())
		return err
	}

	// Attempt delivery
	if err := handler.SendNotification(ctx, notification); err != nil {
		log.Errorf("Failed to send notification via %s: %v", notification.Channel, err)
		ds.updateDeliveryLogStatus(ctx, deliveryLog, model.DeliveryStatusFailed, err.Error())
		return err
	}

	// Mark as delivered
	ds.updateDeliveryLogStatus(ctx, deliveryLog, model.DeliveryStatusDelivered, "Successfully delivered")
	log.Infof("Notification delivered successfully via %s", notification.Channel)
	
	return nil
}

func (ds *deliveryService) RetryFailedDeliveries(ctx context.Context) error {
	log := ds.logger.WithContext(ctx)

	// Get failed deliveries that haven't exceeded max retries
	maxRetries := 3
	failedDeliveries, err := ds.repo.GetFailedDeliveries(ctx, maxRetries, 50)
	if err != nil {
		log.Errorf("Failed to get failed deliveries: %v", err)
		return err
	}

	log.Infof("Retrying %d failed deliveries", len(failedDeliveries))

	for _, deliveryLog := range failedDeliveries {
		// Get the notification
		notification, err := ds.repo.GetNotificationByID(ctx, deliveryLog.NotificationID)
		if err != nil {
			log.Errorf("Failed to get notification %d for retry: %v", deliveryLog.NotificationID, err)
			continue
		}

		// Update retry count
		deliveryLog.RetryCount++
		deliveryLog.Status = model.DeliveryStatusRetrying
		deliveryLog.AttemptedAt = time.Now()

		if err := ds.repo.UpdateDeliveryLog(ctx, deliveryLog); err != nil {
			log.Errorf("Failed to update delivery log for retry: %v", err)
			continue
		}

		// Attempt redelivery
		if err := ds.DeliverNotification(ctx, notification); err != nil {
			log.Errorf("Retry failed for notification %d: %v", notification.ID, err)
			continue
		}

		log.Infof("Successfully retried notification %d", notification.ID)
	}

	return nil
}

func (ds *deliveryService) updateDeliveryLogStatus(ctx context.Context, log *model.NotificationDeliveryLog, status model.DeliveryStatus, response string) {
	log.Status = status
	log.ProviderResponse = response
	
	if status == model.DeliveryStatusDelivered {
		now := time.Now()
		log.DeliveredAt = &now
	}

	if err := ds.repo.UpdateDeliveryLog(ctx, log); err != nil {
		ds.logger.WithContext(ctx).Errorf("Failed to update delivery log status: %v", err)
	}
}

// In-App Channel Handler (stores notifications in database)
type InAppChannelHandler struct {
	logger *logging.Logger
}

func NewInAppChannelHandler(logger *logging.Logger) ChannelHandler {
	return &InAppChannelHandler{
		logger: logger,
	}
}

func (h *InAppChannelHandler) SendNotification(ctx context.Context, notification *model.Notification) error {
	// For in-app notifications, we just need to ensure they're stored in the database
	// which is already done when the notification is created
	h.logger.WithContext(ctx).Infof("In-app notification stored for user %d", notification.UserID)
	return nil
}

func (h *InAppChannelHandler) GetChannelType() model.NotificationChannel {
	return model.ChannelInApp
}

func (h *InAppChannelHandler) IsEnabled() bool {
	return true // In-app notifications are always enabled
}

// Email Channel Handler (placeholder implementation)
type EmailChannelHandler struct {
	logger  *logging.Logger
	config  *config.Config
	enabled bool
}

func NewEmailChannelHandler(logger *logging.Logger, cfg *config.Config) ChannelHandler {
	// Check if email configuration is available
	enabled := cfg != nil // Simplified check
	
	return &EmailChannelHandler{
		logger:  logger,
		config:  cfg,
		enabled: enabled,
	}
}

func (h *EmailChannelHandler) SendNotification(ctx context.Context, notification *model.Notification) error {
	log := h.logger.WithContext(ctx)
	
	// This is a placeholder implementation
	// In a real implementation, you would:
	// 1. Get user email from user service
	// 2. Format email content
	// 3. Send via SMTP or email service provider
	
	log.Infof("Sending email notification to user %d: %s", notification.UserID, notification.Title)
	
	// Simulate email sending delay
	time.Sleep(100 * time.Millisecond)
	
	// For now, just log the email content
	log.Infof("Email sent successfully - Title: %s, Message: %s", notification.Title, notification.Message)
	
	return nil
}

func (h *EmailChannelHandler) GetChannelType() model.NotificationChannel {
	return model.ChannelEmail
}

func (h *EmailChannelHandler) IsEnabled() bool {
	return h.enabled
}

// Push Channel Handler (placeholder implementation)
type PushChannelHandler struct {
	logger  *logging.Logger
	config  *config.Config
	enabled bool
}

func NewPushChannelHandler(logger *logging.Logger, cfg *config.Config) ChannelHandler {
	// Check if push notification configuration is available
	enabled := cfg != nil // Simplified check
	
	return &PushChannelHandler{
		logger:  logger,
		config:  cfg,
		enabled: enabled,
	}
}

func (h *PushChannelHandler) SendNotification(ctx context.Context, notification *model.Notification) error {
	log := h.logger.WithContext(ctx)
	
	// This is a placeholder implementation
	// In a real implementation, you would:
	// 1. Get user device tokens
	// 2. Format push notification payload
	// 3. Send via FCM, APNS, etc.
	
	log.Infof("Sending push notification to user %d: %s", notification.UserID, notification.Title)
	
	// Simulate push sending delay
	time.Sleep(50 * time.Millisecond)
	
	log.Infof("Push notification sent successfully - Title: %s", notification.Title)
	
	return nil
}

func (h *PushChannelHandler) GetChannelType() model.NotificationChannel {
	return model.ChannelPush
}

func (h *PushChannelHandler) IsEnabled() bool {
	return h.enabled
}

// SMS Channel Handler (placeholder implementation)
type SMSChannelHandler struct {
	logger  *logging.Logger
	config  *config.Config
	enabled bool
}

func NewSMSChannelHandler(logger *logging.Logger, cfg *config.Config) ChannelHandler {
	// Check if SMS configuration is available
	enabled := cfg != nil // Simplified check
	
	return &SMSChannelHandler{
		logger:  logger,
		config:  cfg,
		enabled: enabled,
	}
}

func (h *SMSChannelHandler) SendNotification(ctx context.Context, notification *model.Notification) error {
	log := h.logger.WithContext(ctx)
	
	// This is a placeholder implementation
	// In a real implementation, you would:
	// 1. Get user phone number
	// 2. Format SMS content (keep it short)
	// 3. Send via Twilio, AWS SNS, etc.
	
	log.Infof("Sending SMS notification to user %d: %s", notification.UserID, notification.Title)
	
	// Simulate SMS sending delay
	time.Sleep(200 * time.Millisecond)
	
	// SMS content should be shorter
	smsContent := fmt.Sprintf("%s - %s", notification.Title, notification.Message)
	if len(smsContent) > 160 {
		smsContent = smsContent[:157] + "..."
	}
	
	log.Infof("SMS sent successfully - Content: %s", smsContent)
	
	return nil
}

func (h *SMSChannelHandler) GetChannelType() model.NotificationChannel {
	return model.ChannelSMS
}

func (h *SMSChannelHandler) IsEnabled() bool {
	return h.enabled
}
