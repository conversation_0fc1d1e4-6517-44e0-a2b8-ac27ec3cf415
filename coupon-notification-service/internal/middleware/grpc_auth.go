package middleware

import (
	"context"

	"gitlab.zalopay.vn/phunn4/coupon-notification-service/internal/clients"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/auth"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/errors"
)

func CreateServiceAuthFunc(authClient *clients.AuthClient) func(context.Context) (context.Context, error) {
	return func(ctx context.Context) (context.Context, error) {
		// Extract client credentials from context
		clientID, clientKey, err := auth.ExtractServiceCredentials(ctx)
		if err != nil {
			return nil, errors.NewUnauthorizedError("missing or invalid service credentials")
		}

		// Validate credentials with auth service
		if err := authClient.ValidateServiceCredentials(ctx, clientID, clientKey); err != nil {
			return nil, errors.NewUnauthorizedError("invalid service credentials")
		}

		// Add client ID to context for downstream use
		ctx = auth.WithClientID(ctx, clientID)
		return ctx, nil
	}
}
