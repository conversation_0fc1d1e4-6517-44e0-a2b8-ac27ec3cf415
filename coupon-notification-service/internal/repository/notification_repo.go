package repository

import (
	"context"
	"time"

	"gitlab.zalopay.vn/phunn4/coupon-notification-service/internal/model"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/database"
)

type NotificationRepository interface {
	// Notification CRUD
	CreateNotification(ctx context.Context, notification *model.Notification) error
	GetNotificationByID(ctx context.Context, id uint64) (*model.Notification, error)
	UpdateNotificationStatus(ctx context.Context, id uint64, status model.NotificationStatus) error
	MarkAsRead(ctx context.Context, id uint64) error
	ListNotificationsByUser(ctx context.Context, userID uint64, limit, offset int) ([]*model.Notification, int64, error)
	GetPendingNotifications(ctx context.Context, limit int) ([]*model.Notification, error)
	GetScheduledNotifications(ctx context.Context, before time.Time, limit int) ([]*model.Notification, error)

	// Template management
	CreateTemplate(ctx context.Context, template *model.NotificationTemplate) error
	GetTemplateByKey(ctx context.Context, templateKey string) (*model.NotificationTemplate, error)
	GetTemplatesByType(ctx context.Context, notificationType model.NotificationType) ([]*model.NotificationTemplate, error)
	UpdateTemplate(ctx context.Context, template *model.NotificationTemplate) error
	ListActiveTemplates(ctx context.Context) ([]*model.NotificationTemplate, error)

	// User preferences
	CreateOrUpdatePreference(ctx context.Context, preference *model.UserNotificationPreference) error
	GetUserPreference(ctx context.Context, userID uint64, notificationType model.NotificationType) (*model.UserNotificationPreference, error)
	GetUserPreferences(ctx context.Context, userID uint64) ([]*model.UserNotificationPreference, error)
	DeleteUserPreference(ctx context.Context, userID uint64, notificationType model.NotificationType) error

	// Delivery logs
	CreateDeliveryLog(ctx context.Context, log *model.NotificationDeliveryLog) error
	UpdateDeliveryLog(ctx context.Context, log *model.NotificationDeliveryLog) error
	GetDeliveryLogsByNotification(ctx context.Context, notificationID uint64) ([]*model.NotificationDeliveryLog, error)
	GetFailedDeliveries(ctx context.Context, maxRetries int, limit int) ([]*model.NotificationDeliveryLog, error)
}

type notificationRepository struct {
	db *database.DB
}

func NewNotificationRepository(db *database.DB) NotificationRepository {
	return &notificationRepository{db: db}
}

// Notification CRUD implementations
func (r *notificationRepository) CreateNotification(ctx context.Context, notification *model.Notification) error {
	return r.db.WithContext(ctx).Create(notification).Error
}

func (r *notificationRepository) GetNotificationByID(ctx context.Context, id uint64) (*model.Notification, error) {
	var notification model.Notification
	err := r.db.WithContext(ctx).First(&notification, id).Error
	if err != nil {
		return nil, err
	}
	return &notification, nil
}

func (r *notificationRepository) UpdateNotificationStatus(ctx context.Context, id uint64, status model.NotificationStatus) error {
	updates := map[string]interface{}{
		"status":     status,
		"updated_at": time.Now(),
	}
	
	if status == model.NotificationStatusSent {
		updates["sent_at"] = time.Now()
	}
	
	return r.db.WithContext(ctx).Model(&model.Notification{}).Where("id = ?", id).Updates(updates).Error
}

func (r *notificationRepository) MarkAsRead(ctx context.Context, id uint64) error {
	now := time.Now()
	return r.db.WithContext(ctx).Model(&model.Notification{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"status":     model.NotificationStatusRead,
			"read_at":    &now,
			"updated_at": now,
		}).Error
}

func (r *notificationRepository) ListNotificationsByUser(ctx context.Context, userID uint64, limit, offset int) ([]*model.Notification, int64, error) {
	var notifications []*model.Notification
	var total int64

	// Get total count
	if err := r.db.WithContext(ctx).Model(&model.Notification{}).Where("user_id = ?", userID).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get paginated results
	err := r.db.WithContext(ctx).
		Where("user_id = ?", userID).
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&notifications).Error

	return notifications, total, err
}

func (r *notificationRepository) GetPendingNotifications(ctx context.Context, limit int) ([]*model.Notification, error) {
	var notifications []*model.Notification
	err := r.db.WithContext(ctx).
		Where("status = ? AND (scheduled_at IS NULL OR scheduled_at <= ?)", model.NotificationStatusPending, time.Now()).
		Order("created_at ASC").
		Limit(limit).
		Find(&notifications).Error
	return notifications, err
}

func (r *notificationRepository) GetScheduledNotifications(ctx context.Context, before time.Time, limit int) ([]*model.Notification, error) {
	var notifications []*model.Notification
	err := r.db.WithContext(ctx).
		Where("status = ? AND scheduled_at IS NOT NULL AND scheduled_at <= ?", model.NotificationStatusPending, before).
		Order("scheduled_at ASC").
		Limit(limit).
		Find(&notifications).Error
	return notifications, err
}

// Template management implementations
func (r *notificationRepository) CreateTemplate(ctx context.Context, template *model.NotificationTemplate) error {
	return r.db.WithContext(ctx).Create(template).Error
}

func (r *notificationRepository) GetTemplateByKey(ctx context.Context, templateKey string) (*model.NotificationTemplate, error) {
	var template model.NotificationTemplate
	err := r.db.WithContext(ctx).Where("template_key = ? AND is_active = ?", templateKey, true).First(&template).Error
	if err != nil {
		return nil, err
	}
	return &template, nil
}

func (r *notificationRepository) GetTemplatesByType(ctx context.Context, notificationType model.NotificationType) ([]*model.NotificationTemplate, error) {
	var templates []*model.NotificationTemplate
	err := r.db.WithContext(ctx).Where("type = ? AND is_active = ?", notificationType, true).Find(&templates).Error
	return templates, err
}

func (r *notificationRepository) UpdateTemplate(ctx context.Context, template *model.NotificationTemplate) error {
	template.UpdatedAt = time.Now()
	return r.db.WithContext(ctx).Save(template).Error
}

func (r *notificationRepository) ListActiveTemplates(ctx context.Context) ([]*model.NotificationTemplate, error) {
	var templates []*model.NotificationTemplate
	err := r.db.WithContext(ctx).Where("is_active = ?", true).Find(&templates).Error
	return templates, err
}

// User preferences implementations
func (r *notificationRepository) CreateOrUpdatePreference(ctx context.Context, preference *model.UserNotificationPreference) error {
	var existing model.UserNotificationPreference
	err := r.db.WithContext(ctx).Where("user_id = ? AND type = ?", preference.UserID, preference.Type).First(&existing).Error
	
	if err != nil {
		// Create new preference
		return r.db.WithContext(ctx).Create(preference).Error
	}
	
	// Update existing preference
	existing.PreferredChannel = preference.PreferredChannel
	existing.IsEnabled = preference.IsEnabled
	existing.ChannelSettings = preference.ChannelSettings
	existing.UpdatedAt = time.Now()
	
	return r.db.WithContext(ctx).Save(&existing).Error
}

func (r *notificationRepository) GetUserPreference(ctx context.Context, userID uint64, notificationType model.NotificationType) (*model.UserNotificationPreference, error) {
	var preference model.UserNotificationPreference
	err := r.db.WithContext(ctx).Where("user_id = ? AND type = ?", userID, notificationType).First(&preference).Error
	if err != nil {
		return nil, err
	}
	return &preference, nil
}

func (r *notificationRepository) GetUserPreferences(ctx context.Context, userID uint64) ([]*model.UserNotificationPreference, error) {
	var preferences []*model.UserNotificationPreference
	err := r.db.WithContext(ctx).Where("user_id = ?", userID).Find(&preferences).Error
	return preferences, err
}

func (r *notificationRepository) DeleteUserPreference(ctx context.Context, userID uint64, notificationType model.NotificationType) error {
	return r.db.WithContext(ctx).Where("user_id = ? AND type = ?", userID, notificationType).Delete(&model.UserNotificationPreference{}).Error
}

// Delivery logs implementations
func (r *notificationRepository) CreateDeliveryLog(ctx context.Context, log *model.NotificationDeliveryLog) error {
	return r.db.WithContext(ctx).Create(log).Error
}

func (r *notificationRepository) UpdateDeliveryLog(ctx context.Context, log *model.NotificationDeliveryLog) error {
	return r.db.WithContext(ctx).Save(log).Error
}

func (r *notificationRepository) GetDeliveryLogsByNotification(ctx context.Context, notificationID uint64) ([]*model.NotificationDeliveryLog, error) {
	var logs []*model.NotificationDeliveryLog
	err := r.db.WithContext(ctx).Where("notification_id = ?", notificationID).Order("attempted_at DESC").Find(&logs).Error
	return logs, err
}

func (r *notificationRepository) GetFailedDeliveries(ctx context.Context, maxRetries int, limit int) ([]*model.NotificationDeliveryLog, error) {
	var logs []*model.NotificationDeliveryLog
	err := r.db.WithContext(ctx).
		Where("status = ? AND retry_count < ?", model.DeliveryStatusFailed, maxRetries).
		Order("attempted_at ASC").
		Limit(limit).
		Find(&logs).Error
	return logs, err
}
