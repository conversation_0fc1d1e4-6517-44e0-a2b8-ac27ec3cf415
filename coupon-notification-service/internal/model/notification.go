package model

import (
	"time"

	"gorm.io/datatypes"
)

type NotificationStatus string
type NotificationChannel string
type NotificationType string

const (
	NotificationStatusPending   NotificationStatus = "PENDING"
	NotificationStatusSent      NotificationStatus = "SENT"
	NotificationStatusFailed    NotificationStatus = "FAILED"
	NotificationStatusRead      NotificationStatus = "READ"
	NotificationStatusCancelled NotificationStatus = "CANCELLED"
)

const (
	ChannelEmail NotificationChannel = "EMAIL"
	ChannelInApp NotificationChannel = "IN_APP"
)

const (
	TypeVoucherCreated    NotificationType = "VOUCHER_CREATED"
	TypeVoucherExpiring   NotificationType = "VOUCHER_EXPIRING"
	TypeVoucherUsed       NotificationType = "VOUCHER_USED"
	TypeOrderConfirmation NotificationType = "ORDER_CONFIRMATION"
	TypeVoucherApplied    NotificationType = "VOUCHER_APPLIED"
	TypeVoucherFailed     NotificationType = "VOUCHER_FAILED"
	TypeUserWelcome       NotificationType = "USER_WELCOME"
	TypeUserTypeUpgrade   NotificationType = "USER_TYPE_UPGRADE"
)

type Notification struct {
	ID          uint64              `gorm:"primaryKey;autoIncrement"`
	UserID      uint64              `gorm:"not null;index"`
	Type        NotificationType    `gorm:"type:varchar(50);not null"`
	Title       string              `gorm:"type:varchar(255);not null"`
	Message     string              `gorm:"type:text;not null"`
	Status      NotificationStatus  `gorm:"type:varchar(20);not null;default:'PENDING'"`
	Channel     NotificationChannel `gorm:"type:varchar(20);not null"`
	Metadata    datatypes.JSON      `gorm:"type:jsonb"`
	ScheduledAt *time.Time          `gorm:"index"`
	SentAt      *time.Time
	ReadAt      *time.Time
	CreatedAt   time.Time `gorm:"not null"`
	UpdatedAt   time.Time `gorm:"not null"`
}

func (Notification) TableName() string { return "notifications" }

type NotificationTemplate struct {
	ID                uint64              `gorm:"primaryKey;autoIncrement"`
	TemplateKey       string              `gorm:"type:varchar(100);not null;unique"`
	Type              NotificationType    `gorm:"type:varchar(50);not null"`
	TitleTemplate     string              `gorm:"type:text;not null"`
	MessageTemplate   string              `gorm:"type:text;not null"`
	DefaultChannel    NotificationChannel `gorm:"type:varchar(20);not null"`
	TemplateVariables datatypes.JSON      `gorm:"type:jsonb"`
	IsActive          bool                `gorm:"not null;default:true"`
	CreatedAt         time.Time           `gorm:"not null"`
	UpdatedAt         time.Time           `gorm:"not null"`
}

func (NotificationTemplate) TableName() string { return "notification_templates" }

type UserNotificationPreference struct {
	ID               uint64              `gorm:"primaryKey;autoIncrement"`
	UserID           uint64              `gorm:"not null;index"`
	Type             NotificationType    `gorm:"type:varchar(50);not null"`
	PreferredChannel NotificationChannel `gorm:"type:varchar(20);not null"`
	IsEnabled        bool                `gorm:"not null;default:true"`
	ChannelSettings  datatypes.JSON      `gorm:"type:jsonb"`
	CreatedAt        time.Time           `gorm:"not null"`
	UpdatedAt        time.Time           `gorm:"not null"`
}

func (UserNotificationPreference) TableName() string { return "user_notification_preferences" }

// Event payload structures for different event types
type VoucherCreatedPayload struct {
	VoucherID           uint64    `json:"voucher_id"`
	VoucherCode         string    `json:"voucher_code"`
	Title               string    `json:"title"`
	CreatedBy           uint64    `json:"created_by"`
	ValidFrom           time.Time `json:"valid_from"`
	ValidUntil          time.Time `json:"valid_until"`
	DiscountValue       float64   `json:"discount_value"`
	UserEligibilityType string    `json:"user_eligibility_type"`
}

type VoucherUsedPayload struct {
	VoucherID      uint64  `json:"voucher_id"`
	VoucherCode    string  `json:"voucher_code"`
	UserID         uint64  `json:"user_id"`
	OrderID        uint64  `json:"order_id"`
	DiscountAmount float64 `json:"discount_amount"`
	OrderAmount    float64 `json:"order_amount"`
}

type VoucherExpiringPayload struct {
	VoucherID        uint64    `json:"voucher_id"`
	VoucherCode      string    `json:"voucher_code"`
	ExpiresAt        time.Time `json:"expires_at"`
	HoursUntilExpiry int32     `json:"hours_until_expiry"`
	EligibleUserIDs  []uint64  `json:"eligible_user_ids"`
}

type OrderCreatedPayload struct {
	OrderID     uint64  `json:"order_id"`
	UserID      uint64  `json:"user_id"`
	OrderAmount float64 `json:"order_amount"`
	Status      string  `json:"status"`
}

type VoucherAppliedPayload struct {
	OrderID        uint64  `json:"order_id"`
	UserID         uint64  `json:"user_id"`
	VoucherID      uint64  `json:"voucher_id"`
	VoucherCode    string  `json:"voucher_code"`
	OriginalAmount float64 `json:"original_amount"`
	DiscountAmount float64 `json:"discount_amount"`
	FinalAmount    float64 `json:"final_amount"`
}

type UserCreatedPayload struct {
	UserID   uint64 `json:"user_id"`
	Email    string `json:"email"`
	Name     string `json:"name"`
	UserType string `json:"user_type"`
}

type UserTypeChangedPayload struct {
	UserID  uint64 `json:"user_id"`
	Email   string `json:"email"`
	OldType string `json:"old_type"`
	NewType string `json:"new_type"`
	Reason  string `json:"reason"`
}

// Helper functions
func NewNotification(userID uint64, notificationType NotificationType, title, message string, channel NotificationChannel) *Notification {
	return &Notification{
		UserID:    userID,
		Type:      notificationType,
		Title:     title,
		Message:   message,
		Status:    NotificationStatusPending,
		Channel:   channel,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}
}

func NewScheduledNotification(userID uint64, notificationType NotificationType, title, message string, channel NotificationChannel, scheduledAt time.Time) *Notification {
	notification := NewNotification(userID, notificationType, title, message, channel)
	notification.ScheduledAt = &scheduledAt
	return notification
}

func NewNotificationTemplate(templateKey string, notificationType NotificationType, titleTemplate, messageTemplate string, defaultChannel NotificationChannel) *NotificationTemplate {
	return &NotificationTemplate{
		TemplateKey:     templateKey,
		Type:            notificationType,
		TitleTemplate:   titleTemplate,
		MessageTemplate: messageTemplate,
		DefaultChannel:  defaultChannel,
		IsActive:        true,
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}
}

func NewUserNotificationPreference(userID uint64, notificationType NotificationType, preferredChannel NotificationChannel) *UserNotificationPreference {
	return &UserNotificationPreference{
		UserID:           userID,
		Type:             notificationType,
		PreferredChannel: preferredChannel,
		IsEnabled:        true,
		CreatedAt:        time.Now(),
		UpdatedAt:        time.Now(),
	}
}

func GetAllModels() []any {
	return []any{
		&Notification{},
		&NotificationTemplate{},
		&UserNotificationPreference{},
	}
}
