# Coupon Microservice Infrastructure Management

.PHONY: help infrastructure-up infrastructure-down infrastructure-logs create-topics clean-kafka services-up services-down full-up full-down

# Default target
help:
	@echo "Available targets:"
	@echo "  infrastructure-up    - Start shared infrastructure (<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)"
	@echo "  infrastructure-down  - Stop shared infrastructure"
	@echo "  infrastructure-logs  - Show infrastructure logs"
	@echo "  create-topics       - Create Kafka topics"
	@echo "  clean-kafka         - Clean Kafka data and recreate topics"
	@echo "  services-up         - Start all microservices"
	@echo "  services-down       - Stop all microservices"
	@echo "  full-up             - Start infrastructure and all services"
	@echo "  full-down           - Stop everything"
	@echo "  status              - Show status of all containers"

# Infrastructure management
infrastructure-up:
	@echo "Starting shared infrastructure..."
	docker-compose -f infrastructure-compose.yml up -d
	@echo "Waiting for services to be ready..."
	./scripts/wait-for-services.sh

infrastructure-down:
	@echo "Stopping shared infrastructure..."
	docker-compose -f infrastructure-compose.yml down

infrastructure-logs:
	docker-compose -f infrastructure-compose.yml logs -f

# Kafka topic management
create-topics:
	@echo "Creating Kafka topics..."
	chmod +x scripts/create-topics.sh
	./scripts/create-topics.sh

clean-kafka:
	@echo "Cleaning Kafka data..."
	docker-compose -f infrastructure-compose.yml down
	docker volume rm coupon-microservice_kafka-data coupon-microservice_zookeeper-data coupon-microservice_zookeeper-logs 2>/dev/null || true
	docker-compose -f infrastructure-compose.yml up -d
	./scripts/wait-for-services.sh
	./scripts/create-topics.sh

# Service management
services-up:
	@echo "Starting all microservices..."
	cd coupon-auth-service && docker-compose up -d
	cd coupon-user-service && docker-compose up -d
	cd coupon-product-service && docker-compose up -d
	cd coupon-voucher-service && docker-compose up -d
	cd coupon-order-service && docker-compose up -d
	cd coupon-api-gateway && docker-compose up -d

services-down:
	@echo "Stopping all microservices..."
	cd coupon-auth-service && docker-compose down
	cd coupon-user-service && docker-compose down
	cd coupon-product-service && docker-compose down
	cd coupon-voucher-service && docker-compose down
	cd coupon-order-service && docker-compose down
	cd coupon-api-gateway && docker-compose down

# Full stack management
full-up: infrastructure-up create-topics services-up
	@echo "Full stack is up!"
	@echo "Access points:"
	@echo "  API Gateway: http://localhost:8080"
	@echo "  Kafka UI: http://localhost:8090"
	@echo "  Jaeger: http://localhost:16686"
	@echo "  Adminer: http://localhost:8091"

full-down: services-down infrastructure-down
	@echo "Full stack is down!"

# Status check
status:
	@echo "=== Infrastructure Status ==="
	docker-compose -f infrastructure-compose.yml ps
	@echo ""
	@echo "=== Service Status ==="
	@for service in auth user product voucher order api-gateway; do \
		echo "--- $$service-service ---"; \
		cd coupon-$$service-service 2>/dev/null && docker-compose ps || echo "Service not found"; \
		cd ..; \
	done

# Development helpers
logs-kafka:
	docker logs -f kafka

logs-zookeeper:
	docker logs -f zookeeper

logs-jaeger:
	docker logs -f jaeger

# Cleanup
clean-all:
	@echo "Cleaning up all containers and volumes..."
	docker-compose -f infrastructure-compose.yml down -v
	cd coupon-auth-service && docker-compose down -v
	cd coupon-user-service && docker-compose down -v
	cd coupon-product-service && docker-compose down -v
	cd coupon-voucher-service && docker-compose down -v
	cd coupon-order-service && docker-compose down -v
	cd coupon-api-gateway && docker-compose down -v
	docker system prune -f

# Make scripts executable
setup:
	chmod +x scripts/*.sh
	@echo "Scripts made executable"
